'use client';

import React, { useState } from 'react';
import { useAuth } from '@/components/providers/auth-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  MessageSquare, 
  Search, 
  Star, 
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  TrendingUp,
  FileText,
  DollarSign,
  MapPin,
  Phone,
  Video,
  Download,
  Upload,
  Filter,
  MoreHorizontal,
  Bell,
  Settings,
  User,
  Shield,
  Award,
  Activity
} from 'lucide-react';

const upcomingAppointments = [
  {
    id: 1,
    lawyer: '<PERSON><PERSON>. <PERSON>',
    specialty: 'Derecho de Familia',
    date: '2024-01-15',
    time: '2:00 PM',
    type: 'video',
    status: 'confirmed',
    location: 'Virtual',
    cost: '$250',
    duration: '60 min',
    description: 'Consulta sobre proceso de divorcio'
  },
  {
    id: 2,
    lawyer: '<PERSON><PERSON><PERSON> <PERSON>',
    specialty: 'Derecho Inmobiliario',
    date: '2024-01-18',
    time: '10:30 AM',
    type: 'in-person',
    status: 'pending',
    location: 'San Juan, PR',
    cost: '$200',
    duration: '45 min',
    description: 'Revisión de contrato de compraventa'
  },
  {
    id: 3,
    lawyer: 'Lic. Ana Martínez',
    specialty: 'Derecho Laboral',
    date: '2024-01-22',
    time: '3:30 PM',
    type: 'phone',
    status: 'confirmed',
    location: 'Llamada telefónica',
    cost: '$180',
    duration: '30 min',
    description: 'Seguimiento caso despido injustificado'
  }
];

const recentActivity = [
  {
    id: 1,
    type: 'appointment',
    title: 'Cita completada con Dra. González',
    description: 'Consulta sobre divorcio - Documentos enviados',
    time: '2 horas',
    icon: CheckCircle,
    color: 'text-green-600'
  },
  {
    id: 2,
    type: 'message',
    title: 'Nuevo mensaje de Lic. Rodríguez',
    description: 'Respuesta sobre documentos de propiedad',
    time: '4 horas',
    icon: MessageSquare,
    color: 'text-blue-600'
  },
  {
    id: 3,
    type: 'document',
    title: 'Documento subido',
    description: 'Contrato de trabajo - Caso laboral',
    time: '1 día',
    icon: Upload,
    color: 'text-purple-600'
  },
  {
    id: 4,
    type: 'payment',
    title: 'Pago procesado',
    description: '$250 - Consulta derecho de familia',
    time: '2 días',
    icon: DollarSign,
    color: 'text-green-600'
  }
];

const caseProgress = [
  {
    id: 1,
    title: 'Proceso de Divorcio',
    lawyer: 'Dra. María González',
    status: 'En progreso',
    progress: 65,
    nextStep: 'Audiencia preliminar',
    dueDate: '2024-02-15',
    documents: 8,
    cost: '$2,500'
  },
  {
    id: 2,
    title: 'Compra de Propiedad',
    lawyer: 'Lic. Carlos Rodríguez',
    status: 'Revisión',
    progress: 40,
    nextStep: 'Firma de contrato',
    dueDate: '2024-01-30',
    documents: 12,
    cost: '$1,800'
  },
  {
    id: 3,
    title: 'Caso Laboral',
    lawyer: 'Lic. Ana Martínez',
    status: 'Investigación',
    progress: 25,
    nextStep: 'Recopilación de evidencia',
    dueDate: '2024-03-10',
    documents: 5,
    cost: '$3,200'
  }
];

const recommendedLawyers = [
  {
    id: 1,
    name: 'Dr. Roberto Sánchez',
    specialty: 'Derecho Corporativo',
    rating: 4.9,
    reviews: 156,
    hourlyRate: 350,
    responseTime: '< 2 horas',
    location: 'San Juan',
    image: '/api/placeholder/80/80',
    badges: ['Top Rated', 'Verified'],
    availability: 'Disponible hoy'
  },
  {
    id: 2,
    name: 'Lic. Carmen Vega',
    specialty: 'Derecho de Inmigración',
    rating: 4.8,
    reviews: 203,
    hourlyRate: 275,
    responseTime: '< 4 horas',
    location: 'Bayamón',
    image: '/api/placeholder/80/80',
    badges: ['Specialist', 'Bilingual'],
    availability: 'Próxima cita: Mañana'
  }
];

export default function ClientDashboardProfessional() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                ¡Bienvenido/a, {user?.firstName}!
              </h1>
              <p className="text-gray-600 mt-1">
                Gestiona tus asuntos legales desde un solo lugar
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                Notificaciones
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Configuración
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Casos Activos</p>
                  <p className="text-3xl font-bold text-gray-900">3</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+1 este mes</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Próximas Citas</p>
                  <p className="text-3xl font-bold text-gray-900">2</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Clock className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-blue-600">Esta semana</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Mensajes</p>
                  <p className="text-3xl font-bold text-gray-900">5</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <MessageSquare className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <AlertCircle className="h-4 w-4 text-orange-500 mr-1" />
                <span className="text-orange-600">2 sin leer</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Gastos del Mes</p>
                  <p className="text-3xl font-bold text-gray-900">$1,230</p>
                </div>
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Activity className="h-4 w-4 text-gray-500 mr-1" />
                <span className="text-gray-600">3 pagos</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
            <TabsTrigger value="overview">Resumen</TabsTrigger>
            <TabsTrigger value="appointments">Citas</TabsTrigger>
            <TabsTrigger value="cases">Casos</TabsTrigger>
            <TabsTrigger value="lawyers">Abogados</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-6">
              {/* Upcoming Appointments */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Próximas Citas</span>
                    <Button size="sm" variant="outline">
                      <Plus className="h-4 w-4 mr-1" />
                      Nueva
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingAppointments.slice(0, 3).map((appointment) => (
                      <div key={appointment.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          appointment.type === 'video' ? 'bg-blue-100' :
                          appointment.type === 'phone' ? 'bg-green-100' : 'bg-purple-100'
                        }`}>
                          {appointment.type === 'video' && <Video className="h-6 w-6 text-blue-600" />}
                          {appointment.type === 'phone' && <Phone className="h-6 w-6 text-green-600" />}
                          {appointment.type === 'in-person' && <MapPin className="h-6 w-6 text-purple-600" />}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {appointment.lawyer}
                          </p>
                          <p className="text-sm text-gray-500">{appointment.specialty}</p>
                          <p className="text-xs text-gray-400">
                            {appointment.date} • {appointment.time}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge className={
                            appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                            appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }>
                            {appointment.status === 'confirmed' ? 'Confirmada' :
                             appointment.status === 'pending' ? 'Pendiente' : 'Cancelada'}
                          </Badge>
                          <p className="text-sm font-medium text-gray-900 mt-1">{appointment.cost}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>Actividad Reciente</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-start space-x-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center bg-gray-100`}>
                          <activity.icon className={`h-5 w-5 ${activity.color}`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                          <p className="text-sm text-gray-500">{activity.description}</p>
                          <p className="text-xs text-gray-400 mt-1">Hace {activity.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Case Progress */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Progreso de Casos</CardTitle>
                <CardDescription>
                  Seguimiento en tiempo real de tus asuntos legales
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {caseProgress.map((case_) => (
                    <div key={case_.id} className="p-6 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900">{case_.title}</h4>
                          <p className="text-sm text-gray-600">Con {case_.lawyer}</p>
                        </div>
                        <div className="text-right">
                          <Badge className="mb-2">{case_.status}</Badge>
                          <p className="text-sm font-medium text-gray-900">{case_.cost}</p>
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <div className="flex justify-between text-sm mb-2">
                          <span className="text-gray-600">Progreso</span>
                          <span className="font-medium text-gray-900">{case_.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${case_.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Próximo paso:</span>
                          <p className="font-medium text-gray-900">{case_.nextStep}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Fecha límite:</span>
                          <p className="font-medium text-gray-900">{case_.dueDate}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Documentos:</span>
                          <p className="font-medium text-gray-900">{case_.documents} archivos</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other tabs content would go here */}
          <TabsContent value="appointments">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Gestión de Citas</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Contenido de citas detallado...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cases">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Mis Casos</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Contenido de casos detallado...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="lawyers">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Abogados Recomendados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  {recommendedLawyers.map((lawyer) => (
                    <div key={lawyer.id} className="p-6 bg-gray-50 rounded-lg">
                      <div className="flex items-start space-x-4">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-8 w-8 text-gray-400" />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-lg font-semibold text-gray-900">{lawyer.name}</h4>
                          <p className="text-sm text-gray-600 mb-2">{lawyer.specialty}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-400 mr-1" />
                              <span>{lawyer.rating} ({lawyer.reviews})</span>
                            </div>
                            <span>${lawyer.hourlyRate}/hora</span>
                          </div>
                          <div className="flex flex-wrap gap-2 mb-3">
                            {lawyer.badges.map((badge) => (
                              <Badge key={badge} variant="secondary" className="text-xs">
                                {badge}
                              </Badge>
                            ))}
                          </div>
                          <Button size="sm" className="btn-legal">
                            Programar Consulta
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
