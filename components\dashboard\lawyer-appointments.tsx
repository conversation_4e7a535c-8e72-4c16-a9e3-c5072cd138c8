'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  Clock,
  MapPin,
  Phone,
  Mail,
  Plus,
  CheckCircle,
  AlertCircle,
  XCircle,
  User,
  Filter,
  Search,
  Settings,
  X
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { getFirebaseDb } from '@/lib/firebase';

export function LawyerAppointments() {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, pending, confirmed, completed, cancelled
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);
  const [availability, setAvailability] = useState({
    monday: { enabled: true, start: '09:00', end: '17:00' },
    tuesday: { enabled: true, start: '09:00', end: '17:00' },
    wednesday: { enabled: true, start: '09:00', end: '17:00' },
    thursday: { enabled: true, start: '09:00', end: '17:00' },
    friday: { enabled: true, start: '09:00', end: '15:00' },
    saturday: { enabled: false, start: '09:00', end: '13:00' },
    sunday: { enabled: false, start: '09:00', end: '13:00' }
  });

  useEffect(() => {
    if (user) {
      loadAppointments();
    }
  }, [user]);

  const loadAppointments = useCallback(async () => {
    try {
      setLoading(true);
      
      const appointmentsQuery = query(
        collection(getFirebaseDb(), 'appointments'),
        where('lawyerId', '==', user!.id),
        orderBy('date', 'desc')
      );
      
      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      const appointmentsData = appointmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setAppointments(appointmentsData);
    } catch (error) {
      console.error('Error loading appointments:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  const loadAvailability = useCallback(async () => {
    try {
      const { doc, getDoc } = await import('firebase/firestore');
      const lawyerDoc = await getDoc(doc(getFirebaseDb(), 'lawyers', user!.id));

      if (lawyerDoc.exists()) {
        const lawyerData = lawyerDoc.data();
        if (lawyerData.availability) {
          // Convert string format to object format
          const availabilityObj: any = {};
          Object.keys(lawyerData.availability).forEach(day => {
            const timeRange = lawyerData.availability[day];
            if (timeRange === 'Cerrado') {
              availabilityObj[day] = { enabled: false, start: '09:00', end: '17:00' };
            } else {
              const [start, end] = timeRange.split(' - ');
              availabilityObj[day] = {
                enabled: true,
                start: convertTo24Hour(start),
                end: convertTo24Hour(end)
              };
            }
          });
          setAvailability(availabilityObj);
        }
      }
    } catch (error) {
      console.error('Error loading availability:', error);
    }
  }, [user]);

  const convertTo24Hour = (time12h: string) => {
    const [time, modifier] = time12h.split(' ');
    let [hours, minutes] = time.split(':');
    if (hours === '12') {
      hours = '00';
    }
    if (modifier === 'PM') {
      hours = (parseInt(hours, 10) + 12).toString();
    }
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
  };

  const convertTo12Hour = (time24h: string) => {
    const [hours, minutes] = time24h.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const saveAvailability = async () => {
    try {
      const { doc, updateDoc } = await import('firebase/firestore');

      // Convert availability object to string format for storage
      const availabilityStrings: any = {};
      Object.keys(availability).forEach(day => {
        const dayAvail = (availability as any)[day];
        if (dayAvail.enabled) {
          availabilityStrings[day] = `${convertTo12Hour(dayAvail.start)} - ${convertTo12Hour(dayAvail.end)}`;
        } else {
          availabilityStrings[day] = 'Cerrado';
        }
      });

      await updateDoc(doc(getFirebaseDb(), 'lawyers', user!.id), {
        availability: availabilityStrings,
        updatedAt: new Date().toISOString()
      });

      setShowAvailabilityModal(false);

      // Show success message
      alert('Disponibilidad actualizada exitosamente');
    } catch (error) {
      console.error('Error saving availability:', error);
      alert('Error al guardar la disponibilidad');
    }
  };

  const updateDayAvailability = (day: string, field: string, value: any) => {
    setAvailability(prev => ({
      ...prev,
      [day]: {
        ...(prev as any)[day],
        [field]: value
      }
    }));
  };

  useEffect(() => {
    if (user) {
      loadAppointments();
      loadAvailability();
    }
  }, [user, loadAppointments, loadAvailability]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-400" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-400" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-blue-400" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'text-green-400 border-green-400 bg-green-400/20';
      case 'pending':
        return 'text-yellow-400 border-yellow-400 bg-yellow-400/20';
      case 'cancelled':
        return 'text-red-400 border-red-400 bg-red-400/20';
      case 'completed':
        return 'text-blue-400 border-blue-400 bg-blue-400/20';
      default:
        return 'text-gray-400 border-gray-400 bg-gray-400/20';
    }
  };

  const filteredAppointments = appointments.filter(appointment => {
    if (filter === 'all') return true;
    return appointment.status === filter;
  });

  const getStatusCounts = () => {
    return {
      all: appointments.length,
      pending: appointments.filter(a => a.status === 'pending').length,
      confirmed: appointments.filter(a => a.status === 'confirmed').length,
      completed: appointments.filter(a => a.status === 'completed').length,
      cancelled: appointments.filter(a => a.status === 'cancelled').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent flex items-center">
              <Calendar className="mr-3 h-8 w-8 text-yellow-400" />
              Gestión de Citas
            </h1>
            <p className="mt-3 text-gray-300 text-lg">
              Administra tu calendario y consultas con clientes
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => setShowAvailabilityModal(true)}
              className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-4 py-2 rounded-xl"
            >
              <Settings className="mr-2 h-5 w-5" />
              Configurar Horarios
            </Button>
            <Button className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-semibold px-6 py-3 rounded-2xl">
              <Plus className="mr-2 h-5 w-5" />
              Nueva Cita
            </Button>
          </div>
        </div>
      </div>

      {/* Status Filter Tabs */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-3">
          {[
            { key: 'all', label: 'Todas', count: statusCounts.all },
            { key: 'pending', label: 'Pendientes', count: statusCounts.pending },
            { key: 'confirmed', label: 'Confirmadas', count: statusCounts.confirmed },
            { key: 'completed', label: 'Completadas', count: statusCounts.completed },
            { key: 'cancelled', label: 'Canceladas', count: statusCounts.cancelled },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key)}
              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 ${
                filter === tab.key
                  ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400'
                  : 'bg-black/40 backdrop-blur-xl border border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-400 text-sm font-medium">Hoy</p>
                <p className="text-3xl font-bold text-white">3</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                <Calendar className="h-6 w-6 text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-blue-500/20 rounded-3xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-400 text-sm font-medium">Esta Semana</p>
                <p className="text-3xl font-bold text-white">12</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-2xl flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-green-500/20 rounded-3xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-400 text-sm font-medium">Este Mes</p>
                <p className="text-3xl font-bold text-white">{appointments.length}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-2xl flex items-center justify-center">
                <Calendar className="h-6 w-6 text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-purple-500/20 rounded-3xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-400 text-sm font-medium">Ingresos</p>
                <p className="text-3xl font-bold text-white">${appointments.length * 150}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center">
                <Calendar className="h-6 w-6 text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Appointments List */}
      <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
        <CardHeader className="p-8 border-b border-white/10">
          <CardTitle className="text-white text-xl">
            {filter === 'all' ? 'Todas las Citas' : `Citas ${filter.charAt(0).toUpperCase() + filter.slice(1)}`}
          </CardTitle>
          <CardDescription className="text-gray-400">
            Gestiona y actualiza el estado de tus consultas
          </CardDescription>
        </CardHeader>
        <CardContent className="p-8">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
              <p className="text-gray-400 mt-4 text-lg">Cargando citas...</p>
            </div>
          ) : filteredAppointments.length > 0 ? (
            <div className="space-y-6">
              {filteredAppointments.map((appointment) => (
                <div key={appointment.id} className="group bg-gradient-to-r from-white/5 to-white/10 hover:from-yellow-500/10 hover:to-amber-500/10 border border-white/10 hover:border-yellow-500/30 rounded-2xl p-6 transition-all duration-300">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                        <User className="h-8 w-8 text-yellow-400" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-xl font-bold text-white">
                            {appointment.clientName || 'Cliente'}
                          </h3>
                          <Badge className={`px-3 py-1 rounded-xl font-medium border ${getStatusColor(appointment.status)}`}>
                            {getStatusIcon(appointment.status)}
                            <span className="ml-1 capitalize">{appointment.status || 'Pendiente'}</span>
                          </Badge>
                        </div>
                        
                        <p className="text-gray-300 mb-3">
                          {appointment.specialty || 'Consulta General'}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-300">
                              {appointment.date || 'Fecha por confirmar'}
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-300">
                              {appointment.time || 'Hora por confirmar'}
                            </span>
                          </div>
                          
                          {appointment.clientEmail && (
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-300">{appointment.clientEmail}</span>
                            </div>
                          )}
                          
                          {appointment.clientPhone && (
                            <div className="flex items-center space-x-2">
                              <Phone className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-300">{appointment.clientPhone}</span>
                            </div>
                          )}
                        </div>
                        
                        {appointment.notes && (
                          <div className="mt-4 p-4 bg-white/5 rounded-xl">
                            <p className="text-sm text-gray-300">{appointment.notes}</p>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2">
                      <Button size="sm" className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 hover:border-blue-500/50 rounded-xl">
                        Ver Detalles
                      </Button>
                      {appointment.status === 'pending' && (
                        <>
                          <Button size="sm" className="bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 hover:border-green-500/50 rounded-xl">
                            Confirmar
                          </Button>
                          <Button size="sm" className="bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30 text-red-400 hover:bg-red-500/30 hover:border-red-500/50 rounded-xl">
                            Rechazar
                          </Button>
                        </>
                      )}
                      {appointment.status === 'confirmed' && (
                        <Button size="sm" className="bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/30 hover:border-yellow-400/50 rounded-xl">
                          Completar
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <Calendar className="h-10 w-10 text-gray-500" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                {filter === 'all' ? 'No tienes citas programadas' : `No hay citas ${filter}`}
              </h3>
              <p className="text-gray-400 mb-6">
                Las citas aparecerán aquí cuando los clientes las agenden contigo
              </p>
              <Button className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-8 py-3 rounded-2xl">
                <Settings className="mr-2 h-5 w-5" />
                Configurar Disponibilidad
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Availability Configuration Modal */}
      {showAvailabilityModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <CardHeader className="p-6 border-b border-white/10">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-xl">Configurar Disponibilidad</CardTitle>
                <Button
                  onClick={() => setShowAvailabilityModal(false)}
                  className="bg-white/10 border border-white/20 text-white hover:bg-white/20 hover:border-white/30 rounded-xl px-3 py-2"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <CardDescription className="text-gray-400">
                Configura tus horarios de disponibilidad para que los clientes puedan agendar citas
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {Object.keys(availability).map((day) => {
                  const dayNames = {
                    monday: 'Lunes',
                    tuesday: 'Martes',
                    wednesday: 'Miércoles',
                    thursday: 'Jueves',
                    friday: 'Viernes',
                    saturday: 'Sábado',
                    sunday: 'Domingo'
                  };

                  return (
                    <div key={day} className="bg-white/5 rounded-2xl p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-white font-semibold">{(dayNames as any)[day]}</h3>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={(availability as any)[day].enabled}
                            onChange={(e) => updateDayAvailability(day, 'enabled', e.target.checked)}
                            className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                          />
                          <span className="text-gray-300 text-sm">Disponible</span>
                        </label>
                      </div>

                      {(availability as any)[day].enabled && (
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-gray-400 text-sm mb-2">Hora de inicio</label>
                            <input
                              type="time"
                              value={(availability as any)[day].start}
                              onChange={(e) => updateDayAvailability(day, 'start', e.target.value)}
                              className="w-full bg-black/40 border border-white/20 text-white rounded-xl px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                            />
                          </div>
                          <div>
                            <label className="block text-gray-400 text-sm mb-2">Hora de fin</label>
                            <input
                              type="time"
                              value={(availability as any)[day].end}
                              onChange={(e) => updateDayAvailability(day, 'end', e.target.value)}
                              className="w-full bg-black/40 border border-white/20 text-white rounded-xl px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              <div className="flex justify-end space-x-4 mt-8">
                <Button
                  onClick={() => setShowAvailabilityModal(false)}
                  className="bg-gray-500/20 border border-gray-500/30 text-gray-400 hover:bg-gray-500/30 hover:border-gray-500/50 rounded-xl px-6 py-3"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={saveAvailability}
                  className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-6 py-3 rounded-2xl"
                >
                  Guardar Disponibilidad
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
