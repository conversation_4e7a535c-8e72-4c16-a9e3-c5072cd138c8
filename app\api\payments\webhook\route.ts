import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { PaymentService } from '@/services/payment-service';
import { headers } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = headers().get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe signature' },
        { status: 400 }
      );
    }

    // Verify webhook signature
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
      
      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object);
        break;
      
      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error: any) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 400 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent: any) {
  try {
    const { metadata } = paymentIntent;
    
    // Find payment record by payment intent ID
    const payments = await PaymentService.getUserPayments(metadata.clientId, 'client');
    const payment = payments.find(p => p.paymentIntentId === paymentIntent.id);
    
    if (payment) {
      await PaymentService.updatePaymentStatus(payment.id!, 'succeeded', {
        receiptUrl: paymentIntent.charges?.data[0]?.receipt_url,
        paymentMethod: paymentIntent.charges?.data[0]?.payment_method_details?.type,
        metadata: {
          ...payment.metadata,
          stripeChargeId: paymentIntent.charges?.data[0]?.id
        }
      });

      // Update appointment status if this was for an appointment
      if (metadata.appointmentId) {
        const { updateDoc, doc } = await import('firebase/firestore');
        const { getFirebaseDb } = await import('@/lib/firebase');

        await updateDoc(doc(getFirebaseDb(), 'appointments', metadata.appointmentId), {
          paymentStatus: 'paid',
          status: 'confirmed',
          updatedAt: new Date().toISOString()
        });
      }
    }
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(paymentIntent: any) {
  try {
    const { metadata } = paymentIntent;
    
    const payments = await PaymentService.getUserPayments(metadata.clientId, 'client');
    const payment = payments.find(p => p.paymentIntentId === paymentIntent.id);
    
    if (payment) {
      await PaymentService.updatePaymentStatus(payment.id!, 'failed', {
        metadata: {
          ...payment.metadata,
          failureReason: paymentIntent.last_payment_error?.message || 'Payment failed'
        }
      });

      // Update appointment status if this was for an appointment
      if (metadata.appointmentId) {
        const { updateDoc, doc } = await import('firebase/firestore');
        const { getFirebaseDb } = await import('@/lib/firebase');

        await updateDoc(doc(getFirebaseDb(), 'appointments', metadata.appointmentId), {
          paymentStatus: 'failed',
          status: 'payment_pending',
          updatedAt: new Date().toISOString()
        });
      }
    }
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handlePaymentCanceled(paymentIntent: any) {
  try {
    const { metadata } = paymentIntent;
    
    const payments = await PaymentService.getUserPayments(metadata.clientId, 'client');
    const payment = payments.find(p => p.paymentIntentId === paymentIntent.id);
    
    if (payment) {
      await PaymentService.updatePaymentStatus(payment.id!, 'cancelled');

      // Update appointment status if this was for an appointment
      if (metadata.appointmentId) {
        const { updateDoc, doc } = await import('firebase/firestore');
        const { getFirebaseDb } = await import('@/lib/firebase');

        await updateDoc(doc(getFirebaseDb(), 'appointments', metadata.appointmentId), {
          paymentStatus: 'cancelled',
          status: 'cancelled',
          updatedAt: new Date().toISOString()
        });
      }
    }
  } catch (error) {
    console.error('Error handling payment canceled:', error);
  }
}

async function handleChargeDispute(dispute: any) {
  try {
    // Handle charge disputes
    console.log('Charge dispute created:', dispute);
    
    // You can implement dispute handling logic here
    // For example, notify the lawyer and admin about the dispute
  } catch (error) {
    console.error('Error handling charge dispute:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice: any) {
  try {
    // Handle subscription or invoice payments
    console.log('Invoice payment succeeded:', invoice);
    
    // You can implement subscription logic here if needed
  } catch (error) {
    console.error('Error handling invoice payment:', error);
  }
}
