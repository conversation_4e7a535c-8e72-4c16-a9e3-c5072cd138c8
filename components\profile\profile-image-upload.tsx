'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Camera, 
  Upload, 
  X, 
  User, 
  Loader2,
  Check,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { doc, updateDoc } from 'firebase/firestore';
import { storage, db } from '@/lib/firebase';
import Image from 'next/image';

interface ProfileImageUploadProps {
  currentImageUrl?: string;
  onImageUpdate?: (newImageUrl: string) => void;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function ProfileImageUpload({ 
  currentImageUrl, 
  onImageUpdate, 
  size = 'md',
  className = '' 
}: ProfileImageUploadProps) {
  const { user } = useAuth();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
    xl: 'w-40 h-40'
  };

  const iconSizes = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return 'Solo se permiten archivos JPG, PNG y WebP';
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return 'El archivo debe ser menor a 5MB';
    }

    return null;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError(null);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload file
    uploadImage(file);
  };

  const uploadImage = async (file: File) => {
    if (!user) {
      setError('Usuario no autenticado');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);
    setSuccess(false);

    try {
      // Create a unique filename
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop();
      const fileName = `profile-images/${user.id}/${timestamp}.${fileExtension}`;
      
      // Create storage reference
      const storageRef = ref(storage, fileName);

      // Upload file
      const uploadTask = uploadBytes(storageRef, file);
      
      // Simulate progress (Firebase doesn't provide real-time progress for uploadBytes)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const snapshot = await uploadTask;
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      // Delete old image if exists
      if (currentImageUrl && currentImageUrl.includes('firebase')) {
        try {
          const oldImageRef = ref(storage, currentImageUrl);
          await deleteObject(oldImageRef);
        } catch (deleteError) {
          console.warn('Could not delete old image:', deleteError);
        }
      }

      // Update user document in Firestore
      await updateDoc(doc(db, 'users', user.id), {
        profileImage: downloadURL,
        updatedAt: new Date().toISOString()
      });

      // Update role-specific collection
      if (user.role === 'lawyer') {
        await updateDoc(doc(db, 'lawyers', user.id), {
          profileImage: downloadURL,
          updatedAt: new Date().toISOString()
        });
      } else {
        await updateDoc(doc(db, 'clients', user.id), {
          profileImage: downloadURL,
          updatedAt: new Date().toISOString()
        });
      }

      setSuccess(true);
      onImageUpdate?.(downloadURL);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
        setPreviewUrl(null);
      }, 3000);

    } catch (error: any) {
      console.error('Error uploading image:', error);
      setError('Error al subir la imagen. Intenta de nuevo.');
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleRemoveImage = async () => {
    if (!user || !currentImageUrl) return;

    setIsUploading(true);
    setError(null);

    try {
      // Delete from storage if it's a Firebase URL
      if (currentImageUrl.includes('firebase')) {
        try {
          const imageRef = ref(storage, currentImageUrl);
          await deleteObject(imageRef);
        } catch (deleteError) {
          console.warn('Could not delete image from storage:', deleteError);
        }
      }

      // Update user document
      await updateDoc(doc(db, 'users', user.id), {
        profileImage: '',
        updatedAt: new Date().toISOString()
      });

      // Update role-specific collection
      if (user.role === 'lawyer') {
        await updateDoc(doc(db, 'lawyers', user.id), {
          profileImage: '',
          updatedAt: new Date().toISOString()
        });
      } else {
        await updateDoc(doc(db, 'clients', user.id), {
          profileImage: '',
          updatedAt: new Date().toISOString()
        });
      }

      onImageUpdate?.('');
      setSuccess(true);

      setTimeout(() => setSuccess(false), 3000);

    } catch (error: any) {
      console.error('Error removing image:', error);
      setError('Error al eliminar la imagen');
    } finally {
      setIsUploading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const displayImageUrl = previewUrl || currentImageUrl;

  return (
    <div className={`relative ${className}`}>
      <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-2xl overflow-hidden">
        <CardContent className="p-6">
          <div className="flex flex-col items-center space-y-4">
            {/* Image Display */}
            <div className={`relative ${sizeClasses[size]} rounded-2xl overflow-hidden bg-gradient-to-br from-gray-600/20 to-gray-700/20 border-2 border-dashed border-gray-500/30 flex items-center justify-center group`}>
              {displayImageUrl ? (
                <>
                  <Image
                    src={displayImageUrl}
                    alt="Profile"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  {!isUploading && (
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <Button
                        onClick={triggerFileInput}
                        size="sm"
                        className="bg-white/20 border border-white/30 text-white hover:bg-white/30 rounded-xl mr-2"
                      >
                        <Camera className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={handleRemoveImage}
                        size="sm"
                        className="bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30 rounded-xl"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="flex flex-col items-center justify-center text-gray-400">
                  <User className={iconSizes[size]} />
                  {size !== 'sm' && (
                    <span className="text-xs mt-2 text-center">Sin foto</span>
                  )}
                </div>
              )}

              {/* Upload Progress */}
              {isUploading && (
                <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
                  <div className="text-center">
                    <Loader2 className="h-6 w-6 text-yellow-400 animate-spin mx-auto mb-2" />
                    <div className="text-xs text-white">{uploadProgress}%</div>
                  </div>
                </div>
              )}

              {/* Success Indicator */}
              {success && (
                <div className="absolute inset-0 bg-green-500/20 flex items-center justify-center">
                  <Check className="h-8 w-8 text-green-400" />
                </div>
              )}
            </div>

            {/* Upload Button */}
            {!displayImageUrl && !isUploading && (
              <Button
                onClick={triggerFileInput}
                className="bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/30 hover:border-yellow-400/50 rounded-xl px-4 py-2"
                disabled={isUploading}
              >
                <Upload className="mr-2 h-4 w-4" />
                Subir Foto
              </Button>
            )}

            {/* Error Message */}
            {error && (
              <div className="flex items-center space-x-2 text-red-400 text-sm">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="flex items-center space-x-2 text-green-400 text-sm">
                <Check className="h-4 w-4" />
                <span>Imagen actualizada exitosamente</span>
              </div>
            )}

            {/* File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png,image/webp"
              onChange={handleFileSelect}
              className="hidden"
            />

            {/* Upload Guidelines */}
            {size !== 'sm' && (
              <div className="text-xs text-gray-500 text-center">
                <p>JPG, PNG o WebP. Máximo 5MB.</p>
                <p>Recomendado: 400x400px</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
