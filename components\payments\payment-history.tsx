'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DollarSign,
  Calendar,
  Download,
  Filter,
  Search,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  Eye,
  Receipt
} from 'lucide-react';
import { PaymentService, PaymentData } from '@/services/payment-service';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

interface PaymentHistoryProps {
  userType: 'client' | 'lawyer';
  className?: string;
}

export function PaymentHistory({ userType, className = '' }: PaymentHistoryProps) {
  const { user } = useAuth();
  const [payments, setPayments] = useState<PaymentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'succeeded' | 'pending' | 'failed'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const loadPayments = useCallback(async () => {
    try {
      setLoading(true);
      const userPayments = await PaymentService.getUserPayments(user!.id, userType);
      setPayments(userPayments.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ));
    } catch (error) {
      console.error('Error loading payments:', error);
    } finally {
      setLoading(false);
    }
  }, [user, userType]);

  useEffect(() => {
    if (user) {
      loadPayments();
    }
  }, [user, loadPayments]);

  const filteredPayments = payments.filter(payment => {
    const matchesFilter = filter === 'all' || payment.status === filter;
    const matchesSearch = searchTerm === '' || 
      payment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.metadata?.lawyerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.metadata?.clientName?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  const getStatusIcon = (status: PaymentData['status']) => {
    switch (status) {
      case 'succeeded':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-400" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-400" />;
      case 'processing':
        return <RefreshCw className="h-5 w-5 text-blue-400 animate-spin" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-gray-400" />;
      case 'refunded':
        return <AlertTriangle className="h-5 w-5 text-orange-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: PaymentData['status']) => {
    switch (status) {
      case 'succeeded':
        return 'from-green-400/20 to-green-500/20 border-green-400/30 text-green-400';
      case 'failed':
        return 'from-red-400/20 to-red-500/20 border-red-400/30 text-red-400';
      case 'pending':
        return 'from-yellow-400/20 to-yellow-500/20 border-yellow-400/30 text-yellow-400';
      case 'processing':
        return 'from-blue-400/20 to-blue-500/20 border-blue-400/30 text-blue-400';
      case 'cancelled':
        return 'from-gray-400/20 to-gray-500/20 border-gray-400/30 text-gray-400';
      case 'refunded':
        return 'from-orange-400/20 to-orange-500/20 border-orange-400/30 text-orange-400';
      default:
        return 'from-gray-400/20 to-gray-500/20 border-gray-400/30 text-gray-400';
    }
  };

  const getStatusText = (status: PaymentData['status']) => {
    switch (status) {
      case 'succeeded':
        return 'Completado';
      case 'failed':
        return 'Fallido';
      case 'pending':
        return 'Pendiente';
      case 'processing':
        return 'Procesando';
      case 'cancelled':
        return 'Cancelado';
      case 'refunded':
        return 'Reembolsado';
      default:
        return 'Desconocido';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: es 
      });
    } catch {
      return 'Fecha inválida';
    }
  };

  const totalAmount = filteredPayments
    .filter(p => p.status === 'succeeded')
    .reduce((sum, p) => sum + (userType === 'lawyer' ? p.lawyerAmount : p.amount), 0);

  const pendingAmount = filteredPayments
    .filter(p => p.status === 'pending')
    .reduce((sum, p) => sum + (userType === 'lawyer' ? p.lawyerAmount : p.amount), 0);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-green-500/20 rounded-2xl md:rounded-3xl">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-400 text-sm font-medium">
                  {userType === 'lawyer' ? 'Ingresos' : 'Pagado'}
                </p>
                <p className="text-2xl md:text-3xl font-bold text-white">
                  ${totalAmount.toFixed(2)}
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-xl flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 rounded-2xl md:rounded-3xl">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-400 text-sm font-medium">Pendiente</p>
                <p className="text-2xl md:text-3xl font-bold text-white">
                  ${pendingAmount.toFixed(2)}
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-xl flex items-center justify-center">
                <Clock className="h-6 w-6 text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-blue-500/20 rounded-2xl md:rounded-3xl">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-400 text-sm font-medium">Transacciones</p>
                <p className="text-2xl md:text-3xl font-bold text-white">
                  {filteredPayments.length}
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-xl flex items-center justify-center">
                <Receipt className="h-6 w-6 text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl">
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar transacciones..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 md:py-3 bg-black/40 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50"
              />
            </div>

            {/* Status Filter */}
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'all', label: 'Todas' },
                { value: 'succeeded', label: 'Completadas' },
                { value: 'pending', label: 'Pendientes' },
                { value: 'failed', label: 'Fallidas' }
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => setFilter(option.value as any)}
                  className={`px-3 md:px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                    filter === option.value
                      ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400'
                      : 'bg-black/40 border border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>

            {/* Export Button */}
            <Button className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-4 py-2 md:py-3 rounded-xl whitespace-nowrap">
              <Download className="mr-2 h-4 w-4" />
              <span className="hidden md:inline">Exportar</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payment List */}
      <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl">
        <CardHeader className="p-4 md:p-6 border-b border-white/10">
          <CardTitle className="text-white text-lg md:text-xl">
            Historial de {userType === 'lawyer' ? 'Ingresos' : 'Pagos'}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto"></div>
              <p className="text-gray-400 mt-4">Cargando transacciones...</p>
            </div>
          ) : filteredPayments.length > 0 ? (
            <div className="divide-y divide-white/10">
              {filteredPayments.map((payment) => (
                <div key={payment.id} className="p-4 md:p-6 hover:bg-white/5 transition-colors duration-200">
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-xl flex items-center justify-center flex-shrink-0">
                        {getStatusIcon(payment.status)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-semibold text-sm md:text-base truncate">
                          {payment.description}
                        </h3>
                        <p className="text-gray-400 text-xs md:text-sm mt-1">
                          {userType === 'lawyer' 
                            ? `Cliente: ${payment.metadata?.clientName || 'N/A'}`
                            : `Abogado: ${payment.metadata?.lawyerName || 'N/A'}`
                          }
                        </p>
                        <div className="flex flex-wrap items-center gap-2 mt-2">
                          <Badge className={`bg-gradient-to-r ${getStatusColor(payment.status)} text-xs`}>
                            {getStatusText(payment.status)}
                          </Badge>
                          <span className="text-gray-500 text-xs">
                            {formatTimeAgo(payment.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between md:justify-end md:flex-col md:items-end gap-2">
                      <div className="text-right">
                        <p className="text-white font-bold text-lg">
                          ${(userType === 'lawyer' ? payment.lawyerAmount : payment.amount).toFixed(2)}
                        </p>
                        {userType === 'lawyer' && payment.status === 'succeeded' && (
                          <p className="text-gray-400 text-xs">
                            Tarifa: ${payment.platformFee.toFixed(2)}
                          </p>
                        )}
                      </div>
                      
                      {payment.receiptUrl && (
                        <Button
                          onClick={() => window.open(payment.receiptUrl, '_blank')}
                          className="bg-white/10 border border-white/20 text-white hover:bg-white/20 hover:border-white/30 px-3 py-1 rounded-lg text-xs"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          <span className="hidden md:inline">Recibo</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-3xl flex items-center justify-center mx-auto mb-4">
                <DollarSign className="h-8 w-8 text-gray-500" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">No hay transacciones</h3>
              <p className="text-gray-400">
                {filter === 'all' 
                  ? 'Aún no tienes transacciones registradas'
                  : `No hay transacciones con estado "${getStatusText(filter as any)}"`
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
