'use client';

import React, { useState } from 'react';
import { LawyerDashboardNav } from './lawyer-dashboard-nav';
import { LawyerDashboard } from './lawyer-dashboard';
import { LawyerClients } from './lawyer-clients';
import { LawyerAppointments } from './lawyer-appointments';
import { LawyerMessages } from './lawyer-messages';
import { LawyerProfile } from './lawyer-profile';
import { LawyerSettings } from './lawyer-settings';
import { LawyerReports } from './lawyer-reports';

export function LawyerDashboardLayout() {
  const [activeSection, setActiveSection] = useState('dashboard');

  const renderSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <LawyerDashboard onSectionChange={setActiveSection} />;
      case 'clientes':
        return <LawyerClients />;
      case 'citas':
        return <LawyerAppointments />;
      case 'mensajes':
        return <LawyerMessages />;
      case 'perfil':
        return <LawyerProfile />;
      case 'configuracion':
        return <LawyerSettings />;
      case 'reportes':
        return <LawyerReports />;
      default:
        return <LawyerDashboard onSectionChange={setActiveSection} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
      </div>

      {/* Navigation */}
      <LawyerDashboardNav 
        activeSection={activeSection} 
        onSectionChange={setActiveSection} 
      />

      {/* Main Content */}
      <div className="lg:pl-72">
        <div className="relative z-10">
          {renderSection()}
        </div>
      </div>
    </div>
  );
}
