'use client';

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Lock, 
  Shield, 
  CheckCircle,
  AlertCircle,
  Loader2,
  DollarSign,
  Calendar,
  Star,
  Zap,
  Users,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { SubscriptionService, SubscriptionPlan } from '@/services/subscription-service';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface SubscriptionPaymentProps {
  plan: SubscriptionPlan;
  onSuccess?: (subscriptionId: string) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
}

const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      fontSize: '16px',
      color: '#ffffff',
      backgroundColor: 'transparent',
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSmoothing: 'antialiased',
      '::placeholder': {
        color: '#9ca3af',
      },
      iconColor: '#fbbf24',
    },
    invalid: {
      color: '#ef4444',
      iconColor: '#ef4444',
    },
    complete: {
      color: '#10b981',
      iconColor: '#10b981',
    },
  },
  hidePostalCode: false,
  iconStyle: 'solid' as const,
};

function SubscriptionPaymentContent({
  plan,
  onSuccess,
  onError,
  onCancel
}: SubscriptionPaymentProps) {
  const { user } = useAuth();
  const stripe = useStripe();
  const elements = useElements();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  // Calculate savings for yearly plan
  const monthlyCost = 99.99;
  const yearlySavings = plan.interval === 'year' ? (monthlyCost * 12) - plan.price : 0;

  useEffect(() => {
    createSubscription();
  }, []);

  const createSubscription = async () => {
    try {
      const response = await fetch('/api/subscriptions/create-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: plan.id,
          lawyerId: user?.id,
          lawyerEmail: user?.email,
          lawyerName: `${user?.firstName} ${user?.lastName}`
        }),
      });

      const data = await response.json();
      
      if (data.error) {
        setPaymentError(data.error);
        onError?.(data.error);
      } else {
        setClientSecret(data.clientSecret);
      }
    } catch (error: any) {
      setPaymentError('Error creating subscription. Please try again.');
      onError?.(error.message);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setPaymentError('Card element not found');
      setIsProcessing(false);
      return;
    }

    const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: cardElement,
        billing_details: {
          name: `${user?.firstName} ${user?.lastName}`,
          email: user?.email,
        },
      },
    });

    if (error) {
      setPaymentError(error.message || 'Payment failed');
      onError?.(error.message || 'Payment failed');
    } else if (paymentIntent.status === 'succeeded') {
      setPaymentSuccess(true);
      onSuccess?.(paymentIntent.id);
    }

    setIsProcessing(false);
  };

  if (paymentSuccess) {
    return (
      <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-green-500/20 rounded-3xl max-w-md w-full mx-auto">
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
          <h3 className="text-2xl font-bold text-white mb-4">¡Suscripción Activada!</h3>
          <p className="text-gray-300 mb-6">
            Tu suscripción {plan.name} ha sido activada exitosamente.
          </p>
          <div className="bg-green-500/10 border border-green-500/20 rounded-2xl p-4 mb-6">
            <p className="text-green-400 text-sm">
              Tu perfil ya está visible para los clientes.
            </p>
          </div>
          <Button
            onClick={() => window.location.href = '/es/dashboard/lawyer'}
            className="w-full bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:via-green-600 hover:to-green-700 text-black font-bold py-3 rounded-2xl"
          >
            Ir al Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-md w-full mx-auto space-y-6">
      {/* Plan Summary */}
      <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl">
        <CardHeader className="p-6 border-b border-white/10">
          <CardTitle className="text-white text-xl flex items-center">
            <Star className="mr-3 h-6 w-6 text-yellow-400" />
            {plan.name}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Price */}
            <div className="text-center">
              <div className="text-4xl font-bold text-yellow-400 mb-2">
                ${plan.price.toFixed(2)}
              </div>
              <p className="text-gray-400">
                por {plan.interval === 'month' ? 'mes' : 'año'}
              </p>
              {yearlySavings > 0 && (
                <Badge className="bg-gradient-to-r from-green-400/20 to-green-500/20 border border-green-400/30 text-green-400 mt-2">
                  Ahorra ${yearlySavings.toFixed(2)} al año
                </Badge>
              )}
            </div>

            {/* Features */}
            <div className="space-y-3">
              <h4 className="text-white font-semibold">Incluye:</h4>
              <div className="space-y-2">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Billing Info */}
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-2xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-blue-400" />
                <span className="text-blue-400 text-sm font-medium">Facturación</span>
              </div>
              <p className="text-gray-300 text-sm">
                Se factura {plan.interval === 'month' ? 'mensualmente' : 'anualmente'} el mismo día de cada {plan.interval === 'month' ? 'mes' : 'año'}.
                Puedes cancelar en cualquier momento.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Form */}
      <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-white/10 rounded-3xl">
        <CardHeader className="p-6 border-b border-white/10">
          <CardTitle className="text-white text-xl flex items-center">
            <CreditCard className="mr-3 h-6 w-6 text-blue-400" />
            Información de Pago
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Card Element */}
            <div>
              <label className="block text-white font-medium mb-3">
                Información de la Tarjeta
              </label>
              <div className="p-4 bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl focus-within:border-yellow-500/50 focus-within:ring-2 focus-within:ring-yellow-500/20 transition-all duration-200">
                <CardElement options={CARD_ELEMENT_OPTIONS} />
              </div>
            </div>

            {/* Security Notice */}
            <div className="flex items-center space-x-3 p-4 bg-green-500/10 border border-green-500/20 rounded-2xl">
              <Shield className="h-5 w-5 text-green-400 flex-shrink-0" />
              <div>
                <p className="text-green-400 text-sm font-medium">Pago Seguro</p>
                <p className="text-green-300 text-xs">
                  Protegido por encriptación SSL y Stripe
                </p>
              </div>
            </div>

            {/* Subscription Terms */}
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-2xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle className="h-4 w-4 text-yellow-400" />
                <span className="text-yellow-400 text-sm font-medium">Términos de Suscripción</span>
              </div>
              <ul className="text-gray-300 text-xs space-y-1">
                <li>• Tu perfil será visible para clientes inmediatamente</li>
                <li>• Facturación automática cada {plan.interval === 'month' ? 'mes' : 'año'}</li>
                <li>• Cancela en cualquier momento desde tu dashboard</li>
                <li>• Sin compromisos a largo plazo</li>
              </ul>
            </div>

            {/* Error Message */}
            {paymentError && (
              <div className="flex items-center space-x-3 p-4 bg-red-500/10 border border-red-500/20 rounded-2xl">
                <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
                <p className="text-red-400 text-sm">{paymentError}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                type="submit"
                disabled={!stripe || isProcessing || !clientSecret}
                className="w-full bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold py-4 rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed text-lg"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Procesando Suscripción...
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-5 w-5" />
                    Activar Suscripción - ${plan.price.toFixed(2)}
                  </>
                )}
              </Button>

              {onCancel && (
                <Button
                  type="button"
                  onClick={onCancel}
                  className="w-full bg-gray-500/20 border border-gray-500/30 text-gray-400 hover:bg-gray-500/30 hover:border-gray-500/50 py-3 rounded-2xl"
                >
                  Cancelar
                </Button>
              )}
            </div>

            {/* Powered by Stripe */}
            <div className="text-center">
              <p className="text-gray-500 text-xs">
                Procesado de forma segura por{' '}
                <span className="text-blue-400 font-medium">Stripe</span>
              </p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export function SubscriptionPayment(props: SubscriptionPaymentProps) {
  return (
    <Elements stripe={stripePromise}>
      <SubscriptionPaymentContent {...props} />
    </Elements>
  );
}
