# Enable Firebase Authentication

## Current Issue
❌ **Error**: `auth/configuration-not-found`
📝 **Cause**: Firebase Authentication is not enabled in your Firebase project

## Quick Fix Steps

### 1. Open Firebase Console
🔗 **Direct Link**: https://console.firebase.google.com/project/delawpr/authentication

### 2. Enable Authentication
1. Click on **"Authentication"** in the left sidebar
2. Click **"Get started"** button
3. This will initialize Authentication for your project

### 3. Enable Sign-in Methods
Go to the **"Sign-in method"** tab and enable:

#### ✅ Email/Password
1. Click on **"Email/Password"**
2. Toggle **"Enable"** to ON
3. Click **"Save"**

#### ✅ Google (Optional but Recommended)
1. Click on **"Google"**
2. Toggle **"Enable"** to ON
3. Enter your project support email
4. Click **"Save"**

### 4. Set up Authorized Domains
In the **"Sign-in method"** tab, scroll down to **"Authorized domains"**:
- ✅ `localhost` (should already be there)
- ✅ Add your production domain when ready

### 5. Configure Firestore Security Rules
Go to **Firestore Database** → **Rules** and update:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read lawyer profiles
    match /lawyers/{lawyerId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == lawyerId;
    }
    
    // Allow authenticated users to manage their appointments
    match /appointments/{appointmentId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.clientId || 
         request.auth.uid == resource.data.lawyerId);
    }
  }
}
```

## After Enabling Authentication

### Test the Setup
1. Restart your development server: `npm run dev`
2. Go to http://localhost:3001/es/register
3. Try creating a new account
4. Check if the error is resolved

### Verify in Firebase Console
1. Go to **Authentication** → **Users**
2. You should see new users appear when they register

## Quick Links
- 🔗 **Firebase Console**: https://console.firebase.google.com/project/delawpr
- 🔗 **Authentication**: https://console.firebase.google.com/project/delawpr/authentication
- 🔗 **Firestore**: https://console.firebase.google.com/project/delawpr/firestore

## Common Issues After Setup

### Issue: "auth/weak-password"
**Solution**: Use passwords with at least 6 characters

### Issue: "auth/email-already-in-use"
**Solution**: Normal behavior - user already exists

### Issue: "auth/invalid-email"
**Solution**: Check email format is valid

## Next Steps After Authentication Works
1. Test user registration and login
2. Verify dashboard routing works for different roles
3. Test the complete user flow
4. Set up additional authentication providers if needed
