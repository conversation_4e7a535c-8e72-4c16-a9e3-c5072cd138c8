'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const t = useTranslations('Error');

  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <html>
      <body>
        <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
          <div className="text-center max-w-md space-y-6">
            <h1 className="text-6xl font-bold text-red-500">{t('title')}</h1>
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {t('somethingWentWrong')}
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              {error.message || t('unknownError')}
            </p>
            <div className="flex gap-4 justify-center pt-4">
              <Button 
                onClick={() => reset()}
                className="bg-yellow-500 hover:bg-yellow-600 text-white"
              >
                {t('tryAgain')}
              </Button>
              <Link href="/">
                <Button variant="outline">
                  {t('backToHome')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
