// Run this in the browser console after signing up as a lawyer
// Make sure you're logged in as the lawyer account first

async function setupLawyerProfile() {
  try {
    console.log('Setting up lawyer profile...');
    
    // Get current user
    const { getAuth } = await import('firebase/auth');
    const { getFirestore, doc, setDoc, updateDoc } = await import('firebase/firestore');
    
    const auth = getAuth();
    const db = getFirestore();
    const user = auth.currentUser;
    
    if (!user) {
      console.error('❌ No user logged in. Please sign up/login first.');
      return;
    }
    
    console.log('👤 Current user:', user.email);
    
    // Update lawyer profile with complete data
    console.log('📝 Updating lawyer profile...');
    await setDoc(doc(db, 'lawyers', user.uid), {
      id: user.uid,
      firstName: 'María',
      lastName: 'González',
      email: user.email,
      phone: '(*************',
      specialty: 'family',
      specialtyName: 'Derecho de Familia',
      location: 'San Juan, PR',
      description: 'Especialista en derecho de familia con más de 15 años de experiencia. Casos exitosos en divorcios, custodia y adopción.',
      price: '150',
      priceType: 'por hora',
      languages: ['Español', 'Inglés'],
      education: [
        'Universidad de Puerto Rico - Juris Doctor',
        'Universidad Interamericana - Bachillerato en Ciencias Políticas'
      ],
      experience: '15 años de experiencia en derecho de familia',
      certifications: [
        'Colegio de Abogados de Puerto Rico',
        'Certificación en Mediación Familiar'
      ],
      services: [
        'Divorcios',
        'Custodia de menores',
        'Adopción',
        'Pensión alimentaria',
        'Violencia doméstica'
      ],
      profileImage: '',
      rating: 4.9,
      reviewCount: 127,
      isVerified: true,
      profileVisible: true,
      subscriptionStatus: 'active',
      availability: {
        monday: '9:00 AM - 5:00 PM',
        tuesday: '9:00 AM - 5:00 PM',
        wednesday: '9:00 AM - 5:00 PM',
        thursday: '9:00 AM - 5:00 PM',
        friday: '9:00 AM - 3:00 PM',
        saturday: 'Cerrado',
        sunday: 'Cerrado'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }, { merge: true });
    
    // Create active subscription
    console.log('💳 Creating active subscription...');
    await setDoc(doc(db, 'lawyerSubscriptions', user.uid), {
      id: user.uid,
      lawyerId: user.uid,
      plan: 'monthly',
      status: 'active',
      stripeCustomerId: 'dummy_customer_id',
      stripeSubscriptionId: 'dummy_subscription_id',
      currentPeriodStart: new Date().toISOString(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    
    // Update user role to ensure it's set correctly
    console.log('👨‍💼 Updating user role...');
    await updateDoc(doc(db, 'users', user.uid), {
      role: 'lawyer',
      firstName: 'María',
      lastName: 'González',
      phone: '(*************',
      updatedAt: new Date().toISOString()
    });
    
    console.log('✅ Lawyer profile setup complete!');
    console.log('📧 Email: ' + user.email);
    console.log('👤 Name: María González');
    console.log('🏢 Specialty: Derecho de Familia');
    console.log('📍 Location: San Juan, PR');
    console.log('💳 Subscription: Active (Monthly)');
    console.log('🔄 Please refresh the page to see changes.');
    
  } catch (error) {
    console.error('❌ Error setting up lawyer profile:', error);
  }
}

// Run the setup
setupLawyerProfile();
