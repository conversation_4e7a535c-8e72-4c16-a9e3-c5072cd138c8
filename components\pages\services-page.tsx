'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Navigation } from '@/components/layout/navigation';
import Footer from '@/components/layout/footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Scale, 
  Home as HomeIcon, 
  Users, 
  FileText, 
  Shield, 
  Building, 
  Briefcase,
  Heart,
  Gavel,
  DollarSign,
  Globe,
  ChevronRight
} from 'lucide-react';

const legalServices = [
  {
    id: 'criminal',
    icon: <Shield className="h-8 w-8" />,
    title: 'Derecho Penal',
    description: 'Defensa contra cargos criminales, representación en corte y asesoría legal en asuntos penales.',
    features: ['Defensa criminal', 'Representación en corte', 'Asesoría legal', 'Apelaciones'],
    averageRate: '$200-400/hora'
  },
  {
    id: 'family',
    icon: <Heart className="h-8 w-8" />,
    title: 'Derecho de Familia',
    description: '<PERSON><PERSON><PERSON>, custodia de menores, adopción y otros asuntos legales familiares.',
    features: ['Divorcio', 'Custodia de menores', 'Adopción', 'Pensión alimentaria'],
    averageRate: '$150-300/hora'
  },
  {
    id: 'real-estate',
    icon: <HomeIcon className="h-8 w-8" />,
    title: 'Derecho Inmobiliario',
    description: 'Transacciones de propiedades, disputas de arrendamiento y contratos inmobiliarios.',
    features: ['Compra/venta', 'Contratos', 'Disputas', 'Títulos de propiedad'],
    averageRate: '$175-350/hora'
  },
  {
    id: 'immigration',
    icon: <Globe className="h-8 w-8" />,
    title: 'Derecho de Inmigración',
    description: 'Solicitudes de visa, ciudadanía, defensa contra deportación y apelaciones migratorias.',
    features: ['Visas', 'Ciudadanía', 'Defensa deportación', 'Reunificación familiar'],
    averageRate: '$180-360/hora'
  },
  {
    id: 'corporate',
    icon: <Building className="h-8 w-8" />,
    title: 'Derecho Corporativo',
    description: 'Formación de empresas, contratos, cumplimiento normativo y gobierno corporativo.',
    features: ['Formación empresas', 'Contratos', 'Cumplimiento', 'Fusiones y adquisiciones'],
    averageRate: '$250-500/hora'
  },
  {
    id: 'personal-injury',
    icon: <Briefcase className="h-8 w-8" />,
    title: 'Lesiones Personales',
    description: 'Reclamos por accidentes, negligencia médica y compensación por lesiones.',
    features: ['Accidentes auto', 'Negligencia médica', 'Accidentes trabajo', 'Compensación'],
    averageRate: 'Honorarios contingentes'
  },
  {
    id: 'employment',
    icon: <Users className="h-8 w-8" />,
    title: 'Derecho Laboral',
    description: 'Disputas laborales, discriminación, despidos injustificados y derechos de trabajadores.',
    features: ['Disputas laborales', 'Discriminación', 'Despidos', 'Derechos trabajadores'],
    averageRate: '$180-350/hora'
  },
  {
    id: 'bankruptcy',
    icon: <DollarSign className="h-8 w-8" />,
    title: 'Bancarrota',
    description: 'Procedimientos de bancarrota, reestructuración de deudas y protección de activos.',
    features: ['Capítulo 7', 'Capítulo 13', 'Reestructuración', 'Protección activos'],
    averageRate: '$200-400/hora'
  },
  {
    id: 'intellectual-property',
    icon: <FileText className="h-8 w-8" />,
    title: 'Propiedad Intelectual',
    description: 'Patentes, marcas registradas, derechos de autor y protección de propiedad intelectual.',
    features: ['Patentes', 'Marcas', 'Derechos autor', 'Licencias'],
    averageRate: '$300-600/hora'
  },
  {
    id: 'tax',
    icon: <Gavel className="h-8 w-8" />,
    title: 'Derecho Tributario',
    description: 'Planificación fiscal, disputas con Hacienda y cumplimiento tributario.',
    features: ['Planificación fiscal', 'Disputas Hacienda', 'Cumplimiento', 'Auditorías'],
    averageRate: '$250-450/hora'
  },
  {
    id: 'estate-planning',
    icon: <Scale className="h-8 w-8" />,
    title: 'Planificación Patrimonial',
    description: 'Testamentos, fideicomisos, planificación sucesoral y administración de patrimonios.',
    features: ['Testamentos', 'Fideicomisos', 'Planificación', 'Administración'],
    averageRate: '$200-400/hora'
  },
  {
    id: 'civil-litigation',
    icon: <Gavel className="h-8 w-8" />,
    title: 'Litigios Civiles',
    description: 'Representación en disputas civiles, demandas y procedimientos judiciales.',
    features: ['Demandas civiles', 'Mediación', 'Arbitraje', 'Representación judicial'],
    averageRate: '$220-450/hora'
  }
];

export default function ServicesPage() {
  const t = useTranslations('services');

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-700 to-blue-800 pt-24 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl">
              Servicios Legales en Puerto Rico
            </h1>
            <p className="mt-6 max-w-3xl text-xl text-blue-100 mx-auto">
              Encuentra abogados especializados en todas las áreas del derecho. 
              Conecta con profesionales calificados para resolver tus necesidades legales.
            </p>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {legalServices.map((service) => (
            <Card key={service.id} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg">
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-50 p-3 rounded-lg group-hover:bg-blue-100 transition-colors">
                    <div className="text-blue-600">
                      {service.icon}
                    </div>
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-900">
                      {service.title}
                    </CardTitle>
                    <p className="text-sm text-blue-600 font-medium">
                      {service.averageRate}
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <CardDescription className="text-gray-600 leading-relaxed">
                  {service.description}
                </CardDescription>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Servicios incluidos:</h4>
                  <ul className="space-y-1">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <Button 
                  className="w-full group-hover:bg-blue-700 transition-colors"
                  variant="legal"
                >
                  Encontrar Abogados
                  <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            ¿No encuentras lo que buscas?
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            Contáctanos y te ayudaremos a encontrar el abogado especializado que necesitas.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="legal" size="lg">
              Contactar Soporte
            </Button>
            <Button variant="outline" size="lg">
              Ver Todos los Abogados
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
