import { Metadata } from 'next';
import { setRequestLocale } from 'next-intl/server';
import { LawyerProfile } from '@/components/lawyers/lawyer-profile';

export const metadata: Metadata = {
  title: 'Perfil de Abogado - Abogo',
  description: 'Perfil detallado del abogado, servicios, reseñas y opciones de contacto',
};

interface LawyerProfilePageProps {
  params: { locale: string; id: string };
}

export default function LawyerProfilePage({ params: { locale, id } }: LawyerProfilePageProps) {
  setRequestLocale(locale);
  
  return <LawyerProfile lawyerId={id} />;
}
