'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  DollarSign,
  Calendar,
  Users,
  Star,
  Download,
  Filter,
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
  FileText,
  Clock,
  Target
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function LawyerReports() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState({
    totalEarnings: 0,
    monthlyEarnings: 0,
    totalAppointments: 0,
    completedAppointments: 0,
    totalClients: 0,
    averageRating: 0,
    totalReviews: 0,
    responseTime: 0,
    conversionRate: 0
  });
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  useEffect(() => {
    if (user) {
      loadReportData();
    }
  }, [user, selectedPeriod]);

  const loadReportData = async () => {
    try {
      setLoading(true);
      
      // Calculate date range based on selected period
      const now = new Date();
      let startDate = new Date();
      
      switch (selectedPeriod) {
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      // Load appointments data
      const appointmentsQuery = query(
        collection(db, 'appointments'),
        where('lawyerId', '==', user.id),
        where('date', '>=', startDate.toISOString()),
        orderBy('date', 'desc')
      );
      
      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      const appointments = appointmentsSnapshot.docs.map(doc => doc.data());
      
      // Load all-time appointments for total calculations
      const allAppointmentsQuery = query(
        collection(db, 'appointments'),
        where('lawyerId', '==', user.id),
        orderBy('date', 'desc')
      );
      
      const allAppointmentsSnapshot = await getDocs(allAppointmentsQuery);
      const allAppointments = allAppointmentsSnapshot.docs.map(doc => doc.data());

      // Load reviews
      const reviewsQuery = query(
        collection(db, 'reviews'),
        where('lawyerId', '==', user.id)
      );
      
      const reviewsSnapshot = await getDocs(reviewsQuery);
      const reviews = reviewsSnapshot.docs.map(doc => doc.data());

      // Calculate metrics
      const completedAppointments = appointments.filter(apt => apt.status === 'completed');
      const totalEarnings = allAppointments
        .filter(apt => apt.status === 'completed')
        .reduce((sum, apt) => sum + (apt.fee || 150), 0);
      
      const monthlyEarnings = completedAppointments
        .reduce((sum, apt) => sum + (apt.fee || 150), 0);

      const uniqueClients = new Set(allAppointments.map(apt => apt.clientId));
      
      const averageRating = reviews.length > 0 
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
        : 0;

      // Mock response time calculation (in hours)
      const responseTime = 2.5;
      
      // Mock conversion rate (inquiries to appointments)
      const conversionRate = 75;

      setReportData({
        totalEarnings,
        monthlyEarnings,
        totalAppointments: allAppointments.length,
        completedAppointments: completedAppointments.length,
        totalClients: uniqueClients.size,
        averageRating,
        totalReviews: reviews.length,
        responseTime,
        conversionRate
      });

    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const periodOptions = [
    { value: 'week', label: 'Última Semana' },
    { value: 'month', label: 'Último Mes' },
    { value: 'quarter', label: 'Último Trimestre' },
    { value: 'year', label: 'Último Año' }
  ];

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent flex items-center">
              <TrendingUp className="mr-3 h-8 w-8 text-purple-400" />
              Reportes y Análisis
            </h1>
            <p className="mt-3 text-gray-300 text-lg">
              Analiza el rendimiento de tu práctica legal
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
            >
              {periodOptions.map(option => (
                <option key={option.value} value={option.value} className="bg-black text-white">
                  {option.label}
                </option>
              ))}
            </select>
            <Button className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-semibold px-6 py-3 rounded-2xl">
              <Download className="mr-2 h-5 w-5" />
              Exportar
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
        {/* Total Earnings */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-green-500/20 rounded-3xl">
          <CardContent className="p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="w-14 h-14 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-2xl flex items-center justify-center">
                <DollarSign className="h-7 w-7 text-green-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-green-500 bg-clip-text text-transparent">
                  {formatCurrency(reportData.totalEarnings)}
                </div>
                <p className="text-xs text-gray-400 font-medium">Total</p>
              </div>
            </div>
            <h3 className="text-lg font-bold text-white mb-2">Ingresos Totales</h3>
            <p className="text-sm text-gray-400 mb-4">Ganancias acumuladas</p>
            <div className="text-sm text-green-400">
              +{formatCurrency(reportData.monthlyEarnings)} este período
            </div>
          </CardContent>
        </Card>

        {/* Appointments */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-blue-500/20 rounded-3xl">
          <CardContent className="p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-2xl flex items-center justify-center">
                <Calendar className="h-7 w-7 text-blue-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-blue-500 bg-clip-text text-transparent">
                  {reportData.totalAppointments}
                </div>
                <p className="text-xs text-gray-400 font-medium">Total</p>
              </div>
            </div>
            <h3 className="text-lg font-bold text-white mb-2">Citas</h3>
            <p className="text-sm text-gray-400 mb-4">Consultas programadas</p>
            <div className="text-sm text-blue-400">
              {reportData.completedAppointments} completadas
            </div>
          </CardContent>
        </Card>

        {/* Clients */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl">
          <CardContent className="p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="w-14 h-14 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                <Users className="h-7 w-7 text-yellow-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent">
                  {reportData.totalClients}
                </div>
                <p className="text-xs text-gray-400 font-medium">Únicos</p>
              </div>
            </div>
            <h3 className="text-lg font-bold text-white mb-2">Clientes</h3>
            <p className="text-sm text-gray-400 mb-4">Base de clientes</p>
            <div className="text-sm text-yellow-400">
              {reportData.conversionRate}% conversión
            </div>
          </CardContent>
        </Card>

        {/* Rating */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-purple-500/20 rounded-3xl">
          <CardContent className="p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="w-14 h-14 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center">
                <Star className="h-7 w-7 text-purple-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-purple-500 bg-clip-text text-transparent">
                  {reportData.averageRating.toFixed(1)}
                </div>
                <p className="text-xs text-gray-400 font-medium">Promedio</p>
              </div>
            </div>
            <h3 className="text-lg font-bold text-white mb-2">Calificación</h3>
            <p className="text-sm text-gray-400 mb-4">Satisfacción del cliente</p>
            <div className="text-sm text-purple-400">
              {reportData.totalReviews} reseñas
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Response Time */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Clock className="mr-3 h-6 w-6 text-orange-400" />
              Tiempo de Respuesta
            </CardTitle>
            <CardDescription className="text-gray-400">
              Promedio de respuesta a consultas
            </CardDescription>
          </CardHeader>
          <CardContent className="p-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-orange-400 mb-2">
                {reportData.responseTime}h
              </div>
              <p className="text-gray-300 mb-4">Tiempo promedio de respuesta</p>
              <Badge className="bg-green-500/20 border border-green-500/30 text-green-400">
                Excelente
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Conversion Rate */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Target className="mr-3 h-6 w-6 text-cyan-400" />
              Tasa de Conversión
            </CardTitle>
            <CardDescription className="text-gray-400">
              Consultas que se convierten en citas
            </CardDescription>
          </CardHeader>
          <CardContent className="p-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-cyan-400 mb-2">
                {reportData.conversionRate}%
              </div>
              <p className="text-gray-300 mb-4">De consultas a citas</p>
              <Badge className="bg-cyan-500/20 border border-cyan-500/30 text-cyan-400">
                Muy Bueno
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Placeholder */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Earnings Chart */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <LineChart className="mr-3 h-6 w-6 text-green-400" />
              Tendencia de Ingresos
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-gray-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">Gráfico de Ingresos</h3>
              <p className="text-gray-400">
                Visualización de ingresos por período disponible próximamente
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Appointments Distribution */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <PieChart className="mr-3 h-6 w-6 text-blue-400" />
              Distribución de Citas
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="text-center py-12">
              <PieChart className="h-16 w-16 text-gray-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">Análisis de Citas</h3>
              <p className="text-gray-400">
                Distribución por tipo de consulta disponible próximamente
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
