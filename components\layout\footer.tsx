'use client';

import React from 'react';
import Link from 'next/link';
import { Scale } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="relative overflow-hidden border-t border-gray-800/50">

      <div className="relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-20 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          {/* Logo y descripción */}
          <div className="md:col-span-1">
            <div className="flex items-center mb-2">
              <Scale className="h-8 w-8 text-yellow-400" />
              <span className="ml-2 text-2xl font-bold bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-400 bg-clip-text text-transparent">Abogo</span>
            </div>
            <p className="text-gray-400 text-xs leading-relaxed">
              La plataforma legal más confiable de Puerto Rico.
            </p>
          </div>

          {/* Secciones alineadas horizontalmente */}
          <div className="md:col-span-4 grid grid-cols-2 md:grid-cols-4 gap-12">
            {/* Servicios Legales */}
            <div>
              <h3 className="text-sm font-semibold text-yellow-400 tracking-wider uppercase mb-4">
                Servicios Legales
              </h3>
              <ul className="space-y-3">
                {['Derecho de Familia', 'Derecho Penal', 'Derecho Laboral', 'Derecho Corporativo'].map((item, index) => (
                  <li key={index}>
                    <Link href="#" className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 flex items-start group text-sm">
                      <svg className="h-3.5 w-3.5 text-yellow-400 mt-1 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      <span>{item}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Soporte */}
            <div>
              <h3 className="text-sm font-semibold text-yellow-400 tracking-wider uppercase mb-4">
                Soporte
              </h3>
              <ul className="space-y-3">
                {['Centro de Ayuda', 'Preguntas Frecuentes', 'Guías Legales', 'Contacto'].map((item, index) => (
                  <li key={index}>
                    <Link href="#" className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 flex items-start group text-sm">
                      <svg className="h-3.5 w-3.5 text-yellow-400 mt-1 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      <span>{item}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Empresa */}
            <div>
              <h3 className="text-sm font-semibold text-yellow-400 tracking-wider uppercase mb-4">
                Empresa
              </h3>
              <ul className="space-y-3">
                {['Acerca de Nosotros', 'Blog Legal', 'Carreras', 'Prensa'].map((item, index) => (
                  <li key={index}>
                    <Link href="#" className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 flex items-start group text-sm">
                      <svg className="h-3.5 w-3.5 text-yellow-400 mt-1 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      <span>{item}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Legal */}
            <div>
              <h3 className="text-sm font-semibold text-yellow-400 tracking-wider uppercase mb-4">
                Legal
              </h3>
              <ul className="space-y-3">
                {['Política de Privacidad', 'Términos de Servicio', 'Política de Cookies', 'Licencias'].map((item, index) => (
                  <li key={index}>
                    <Link href="#" className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 flex items-start group text-sm">
                      <svg className="h-3.5 w-3.5 text-yellow-400 mt-1 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      <span>{item}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
        <div className="mt-16 pt-8 border-t border-gray-800/50">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-500 text-center md:text-left">
              &copy; {new Date().getFullYear()} Abogo. Todos los derechos reservados.
            </p>
            <div className="mt-4 md:mt-0 flex space-x-6">
              <Link href="/terminos" className="text-sm text-gray-500 hover:text-yellow-400 transition-colors duration-200">
                Términos de Servicio
              </Link>
              <Link href="/privacidad" className="text-sm text-gray-500 hover:text-yellow-400 transition-colors duration-200">
                Política de Privacidad
              </Link>
              <Link href="/cookies" className="text-sm text-gray-500 hover:text-yellow-400 transition-colors duration-200">
                Cookies
              </Link>
            </div>
          </div>
          <p className="mt-4 text-center text-xs text-gray-600">
            Hecho con ❤️ en Puerto Rico para ayudar a nuestra comunidad
          </p>
        </div>
      </div>
    </footer>
  );
}
