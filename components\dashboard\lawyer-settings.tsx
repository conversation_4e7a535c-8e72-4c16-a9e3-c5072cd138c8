'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Settings,
  Bell,
  Shield,
  CreditCard,
  Globe,
  Moon,
  Sun,
  Smartphone,
  Mail,
  MessageSquare,
  Calendar,
  DollarSign,
  Users,
  Eye,
  EyeOff,
  Save,
  Briefcase
} from 'lucide-react';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { getFirebaseDb } from '@/lib/firebase';

export function LawyerSettings() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    // Notification Settings
    notifications: {
      email: true,
      push: true,
      sms: false,
      newAppointments: true,
      appointmentReminders: true,
      newMessages: true,
      paymentUpdates: true,
      newReviews: true,
      systemUpdates: false
    },
    // Privacy Settings
    privacy: {
      profileVisible: true,
      showPhone: true,
      showEmail: false,
      allowDirectBooking: true,
      requireApproval: false
    },
    // Business Settings
    business: {
      autoConfirmAppointments: false,
      bufferTime: 15, // minutes between appointments
      maxAdvanceBooking: 30, // days
      cancellationPolicy: 24, // hours
      requireDeposit: false,
      depositAmount: 50
    },
    // Display Settings
    display: {
      theme: 'dark',
      language: 'es',
      timezone: 'America/Puerto_Rico',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h'
    }
  });

  const loadSettings = useCallback(async () => {
    try {
      const settingsDoc = await getDoc(doc(getFirebaseDb(), 'lawyerSettings', user!.id));
      if (settingsDoc.exists()) {
        setSettings(prev => ({
          ...prev,
          ...settingsDoc.data()
        }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadSettings();
    }
  }, [user, loadSettings]);

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...(prev as any)[category],
        [key]: value
      }
    }));
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      await updateDoc(doc(getFirebaseDb(), 'lawyerSettings', user!.id), {
        ...settings,
        updatedAt: new Date().toISOString()
      });
      alert('Configuraciones guardadas exitosamente');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error al guardar las configuraciones');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent flex items-center">
              <Settings className="mr-3 h-8 w-8 text-gray-400" />
              Configuración
            </h1>
            <p className="mt-3 text-gray-300 text-lg">
              Personaliza tu experiencia y configuraciones de la plataforma
            </p>
          </div>
          <Button
            onClick={saveSettings}
            disabled={loading}
            className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-6 py-3 rounded-2xl"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                Guardando...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Guardar Cambios
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Notification Settings */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Bell className="mr-3 h-6 w-6 text-blue-400" />
              Notificaciones
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              {/* Notification Channels */}
              <div>
                <h3 className="text-white font-semibold mb-4">Canales de Notificación</h3>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-300">Email</span>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.notifications.email}
                      onChange={(e) => updateSetting('notifications', 'email', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Smartphone className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-300">Push Notifications</span>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.notifications.push}
                      onChange={(e) => updateSetting('notifications', 'push', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <div className="flex items-center">
                      <MessageSquare className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-300">SMS</span>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.notifications.sms}
                      onChange={(e) => updateSetting('notifications', 'sms', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                </div>
              </div>

              {/* Notification Types */}
              <div>
                <h3 className="text-white font-semibold mb-4">Tipos de Notificación</h3>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Nuevas citas</span>
                    <input
                      type="checkbox"
                      checked={settings.notifications.newAppointments}
                      onChange={(e) => updateSetting('notifications', 'newAppointments', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Recordatorios de citas</span>
                    <input
                      type="checkbox"
                      checked={settings.notifications.appointmentReminders}
                      onChange={(e) => updateSetting('notifications', 'appointmentReminders', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Nuevos mensajes</span>
                    <input
                      type="checkbox"
                      checked={settings.notifications.newMessages}
                      onChange={(e) => updateSetting('notifications', 'newMessages', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Actualizaciones de pago</span>
                    <input
                      type="checkbox"
                      checked={settings.notifications.paymentUpdates}
                      onChange={(e) => updateSetting('notifications', 'paymentUpdates', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Nuevas reseñas</span>
                    <input
                      type="checkbox"
                      checked={settings.notifications.newReviews}
                      onChange={(e) => updateSetting('notifications', 'newReviews', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Settings */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Shield className="mr-3 h-6 w-6 text-green-400" />
              Privacidad y Visibilidad
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              <div>
                <h3 className="text-white font-semibold mb-4">Visibilidad del Perfil</h3>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-300">Perfil visible públicamente</span>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.privacy.profileVisible}
                      onChange={(e) => updateSetting('privacy', 'profileVisible', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Mostrar número de teléfono</span>
                    <input
                      type="checkbox"
                      checked={settings.privacy.showPhone}
                      onChange={(e) => updateSetting('privacy', 'showPhone', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Mostrar email</span>
                    <input
                      type="checkbox"
                      checked={settings.privacy.showEmail}
                      onChange={(e) => updateSetting('privacy', 'showEmail', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                </div>
              </div>

              <div>
                <h3 className="text-white font-semibold mb-4">Configuración de Citas</h3>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Permitir reservas directas</span>
                    <input
                      type="checkbox"
                      checked={settings.privacy.allowDirectBooking}
                      onChange={(e) => updateSetting('privacy', 'allowDirectBooking', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Requerir aprobación manual</span>
                    <input
                      type="checkbox"
                      checked={settings.privacy.requireApproval}
                      onChange={(e) => updateSetting('privacy', 'requireApproval', e.target.checked)}
                      className="w-4 h-4 text-yellow-500 bg-black/40 border-gray-600 rounded focus:ring-yellow-500"
                    />
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Business Settings */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Briefcase className="mr-3 h-6 w-6 text-purple-400" />
              Configuración de Negocio
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              <div>
                <Label className="text-white font-medium mb-2 block">
                  Tiempo de buffer entre citas (minutos)
                </Label>
                <select
                  value={settings.business.bufferTime}
                  onChange={(e) => updateSetting('business', 'bufferTime', parseInt(e.target.value))}
                  className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                >
                  <option value={0} className="bg-black text-white">Sin buffer</option>
                  <option value={15} className="bg-black text-white">15 minutos</option>
                  <option value={30} className="bg-black text-white">30 minutos</option>
                  <option value={60} className="bg-black text-white">1 hora</option>
                </select>
              </div>

              <div>
                <Label className="text-white font-medium mb-2 block">
                  Máximo días de anticipación para reservas
                </Label>
                <select
                  value={settings.business.maxAdvanceBooking}
                  onChange={(e) => updateSetting('business', 'maxAdvanceBooking', parseInt(e.target.value))}
                  className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                >
                  <option value={7} className="bg-black text-white">1 semana</option>
                  <option value={14} className="bg-black text-white">2 semanas</option>
                  <option value={30} className="bg-black text-white">1 mes</option>
                  <option value={60} className="bg-black text-white">2 meses</option>
                  <option value={90} className="bg-black text-white">3 meses</option>
                </select>
              </div>

              <div>
                <Label className="text-white font-medium mb-2 block">
                  Política de cancelación (horas de anticipación)
                </Label>
                <select
                  value={settings.business.cancellationPolicy}
                  onChange={(e) => updateSetting('business', 'cancellationPolicy', parseInt(e.target.value))}
                  className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                >
                  <option value={2} className="bg-black text-white">2 horas</option>
                  <option value={12} className="bg-black text-white">12 horas</option>
                  <option value={24} className="bg-black text-white">24 horas</option>
                  <option value={48} className="bg-black text-white">48 horas</option>
                  <option value={72} className="bg-black text-white">72 horas</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Display Settings */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Globe className="mr-3 h-6 w-6 text-cyan-400" />
              Preferencias de Visualización
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              <div>
                <Label className="text-white font-medium mb-2 block">
                  Idioma
                </Label>
                <select
                  value={settings.display.language}
                  onChange={(e) => updateSetting('display', 'language', e.target.value)}
                  className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                >
                  <option value="es" className="bg-black text-white">Español</option>
                  <option value="en" className="bg-black text-white">English</option>
                </select>
              </div>

              <div>
                <Label className="text-white font-medium mb-2 block">
                  Formato de fecha
                </Label>
                <select
                  value={settings.display.dateFormat}
                  onChange={(e) => updateSetting('display', 'dateFormat', e.target.value)}
                  className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                >
                  <option value="DD/MM/YYYY" className="bg-black text-white">DD/MM/YYYY</option>
                  <option value="MM/DD/YYYY" className="bg-black text-white">MM/DD/YYYY</option>
                  <option value="YYYY-MM-DD" className="bg-black text-white">YYYY-MM-DD</option>
                </select>
              </div>

              <div>
                <Label className="text-white font-medium mb-2 block">
                  Formato de hora
                </Label>
                <select
                  value={settings.display.timeFormat}
                  onChange={(e) => updateSetting('display', 'timeFormat', e.target.value)}
                  className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                >
                  <option value="12h" className="bg-black text-white">12 horas (AM/PM)</option>
                  <option value="24h" className="bg-black text-white">24 horas</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
