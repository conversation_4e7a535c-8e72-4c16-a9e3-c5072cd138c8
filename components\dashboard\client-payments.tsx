'use client';

import React from 'react';
import { PaymentHistory } from '@/components/payments/payment-history';
import { CreditCard } from 'lucide-react';

export function ClientPayments() {
  return (
    <div className="p-4 md:p-8">
      {/* Header */}
      <div className="mb-6 md:mb-8">
        <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent flex items-center">
          <CreditCard className="mr-3 h-6 w-6 md:h-8 md:w-8 text-green-400" />
          Mis Pagos
        </h1>
        <p className="mt-2 md:mt-3 text-gray-300 text-base md:text-lg">
          Historial de pagos y transacciones
        </p>
      </div>

      {/* Payment History */}
      <PaymentHistory userType="client" />
    </div>
  );
}
