import {
  collection,
  addDoc,
  updateDoc,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  deleteDoc
} from 'firebase/firestore';
import { getFirebaseDb } from '@/lib/firebase';
import { NotificationService } from './notification-service';

export interface LawyerSubscription {
  id?: string;
  lawyerId: string;
  lawyerEmail: string;
  lawyerName: string;
  status: 'active' | 'inactive' | 'suspended' | 'cancelled' | 'past_due';
  plan: 'monthly' | 'yearly';
  amount: number;
  currency: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  nextBillingDate: string;
  trialEnd?: string;
  cancelAtPeriodEnd: boolean;
  termsAcceptedAt: string;
  termsVersion: string;
  createdAt: string;
  updatedAt: string;
  suspendedAt?: string;
  cancelledAt?: string;
  metadata?: Record<string, any>;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  stripePriceId?: string;
}

export class SubscriptionService {
  
  // Subscription plans
  static readonly PLANS: SubscriptionPlan[] = [
    {
      id: 'lawyer_monthly',
      name: 'Plan Mensual para Abogados',
      price: 99.99,
      currency: 'usd',
      interval: 'month',
      stripePriceId: process.env.STRIPE_LAWYER_MONTHLY_PRICE_ID,
      features: [
        'Perfil profesional visible',
        'Sistema de citas ilimitadas',
        'Mensajería segura con clientes',
        'Procesamiento de pagos integrado',
        'Reportes y análisis',
        'Soporte técnico prioritario',
        'Calendario personalizado',
        'Notificaciones en tiempo real'
      ]
    },
    {
      id: 'lawyer_yearly',
      name: 'Plan Anual para Abogados',
      price: 999.99,
      currency: 'usd',
      interval: 'year',
      stripePriceId: process.env.STRIPE_LAWYER_YEARLY_PRICE_ID,
      features: [
        'Todos los beneficios del plan mensual',
        '2 meses gratis (ahorro de $200)',
        'Análisis avanzados',
        'Soporte prioritario 24/7',
        'Integración con herramientas externas',
        'Backup automático de datos'
      ]
    }
  ];

  // Create subscription record
  static async createSubscription(subscriptionData: Omit<LawyerSubscription, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const subscription = {
        ...subscriptionData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const docRef = await addDoc(collection(getFirebaseDb(), 'lawyerSubscriptions'), {
        ...subscription,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Update lawyer profile with subscription status
      await this.updateLawyerSubscriptionStatus(subscriptionData.lawyerId, 'active');

      return docRef.id;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  // Update subscription status
  static async updateSubscriptionStatus(
    subscriptionId: string, 
    status: LawyerSubscription['status'],
    additionalData?: Partial<LawyerSubscription>
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: serverTimestamp(),
        ...additionalData
      };

      // Add timestamp for specific status changes
      if (status === 'suspended') {
        updateData.suspendedAt = serverTimestamp();
      } else if (status === 'cancelled') {
        updateData.cancelledAt = serverTimestamp();
      }

      await updateDoc(doc(db, 'lawyerSubscriptions', subscriptionId), updateData);

      // Get subscription to update lawyer profile
      const subscription = await this.getSubscription(subscriptionId);
      if (subscription) {
        await this.updateLawyerSubscriptionStatus(subscription.lawyerId, status);
        await this.sendSubscriptionNotifications(subscription);
      }
    } catch (error) {
      console.error('Error updating subscription status:', error);
      throw error;
    }
  }

  // Update lawyer profile subscription status
  static async updateLawyerSubscriptionStatus(lawyerId: string, status: LawyerSubscription['status']): Promise<void> {
    try {
      const isActive = status === 'active';
      
      await updateDoc(doc(db, 'lawyers', lawyerId), {
        subscriptionStatus: status,
        isActive: isActive,
        profileVisible: isActive, // Hide profile if subscription is not active
        updatedAt: serverTimestamp()
      });

      // Also update user record
      await updateDoc(doc(db, 'users', lawyerId), {
        subscriptionStatus: status,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating lawyer subscription status:', error);
      throw error;
    }
  }

  // Get subscription by ID
  static async getSubscription(subscriptionId: string): Promise<LawyerSubscription | null> {
    try {
      const subscriptionDoc = await getDoc(doc(db, 'lawyerSubscriptions', subscriptionId));
      if (subscriptionDoc.exists()) {
        return { id: subscriptionDoc.id, ...subscriptionDoc.data() } as LawyerSubscription;
      }
      return null;
    } catch (error) {
      console.error('Error getting subscription:', error);
      return null;
    }
  }

  // Get lawyer's active subscription
  static async getLawyerSubscription(lawyerId: string): Promise<LawyerSubscription | null> {
    try {
      const subscriptionsQuery = query(
        collection(db, 'lawyerSubscriptions'),
        where('lawyerId', '==', lawyerId),
        where('status', 'in', ['active', 'past_due'])
      );

      const subscriptionsSnapshot = await getDocs(subscriptionsQuery);
      if (!subscriptionsSnapshot.empty) {
        const doc = subscriptionsSnapshot.docs[0];
        return { id: doc.id, ...doc.data() } as LawyerSubscription;
      }
      return null;
    } catch (error) {
      console.error('Error getting lawyer subscription:', error);
      return null;
    }
  }

  // Check if lawyer has active subscription
  static async hasActiveSubscription(lawyerId: string): Promise<boolean> {
    try {
      const subscription = await this.getLawyerSubscription(lawyerId);
      return subscription?.status === 'active';
    } catch (error) {
      console.error('Error checking active subscription:', error);
      return false;
    }
  }

  // Get all active lawyers (for client-side display)
  static async getActiveLawyers(): Promise<any[]> {
    try {
      const lawyersQuery = query(
        collection(db, 'lawyers'),
        where('subscriptionStatus', '==', 'active'),
        where('profileVisible', '==', true)
      );

      const lawyersSnapshot = await getDocs(lawyersQuery);
      return lawyersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting active lawyers:', error);
      return [];
    }
  }

  // Send subscription-related notifications
  static async sendSubscriptionNotifications(subscription: LawyerSubscription): Promise<void> {
    try {
      switch (subscription.status) {
        case 'active':
          await NotificationService.notifySubscriptionActivated(
            subscription.lawyerId,
            {
              plan: subscription.plan,
              nextBillingDate: subscription.nextBillingDate
            }
          );
          break;

        case 'past_due':
          await NotificationService.notifyPaymentFailed(
            subscription.lawyerId,
            {
              amount: subscription.amount,
              failureReason: 'Pago de suscripción fallido'
            }
          );
          break;

        case 'suspended':
          await NotificationService.notifySubscriptionSuspended(
            subscription.lawyerId,
            {
              reason: 'Falta de pago',
              reactivationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/lawyer/subscription`
            }
          );
          break;

        case 'cancelled':
          await NotificationService.notifySubscriptionCancelled(
            subscription.lawyerId,
            {
              endDate: subscription.currentPeriodEnd
            }
          );
          break;
      }
    } catch (error) {
      console.error('Error sending subscription notifications:', error);
    }
  }

  // Cancel subscription
  static async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<boolean> {
    try {
      const updateData: any = {
        cancelAtPeriodEnd,
        updatedAt: serverTimestamp()
      };

      if (!cancelAtPeriodEnd) {
        updateData.status = 'cancelled';
        updateData.cancelledAt = serverTimestamp();
      }

      await updateDoc(doc(db, 'lawyerSubscriptions', subscriptionId), updateData);

      // If immediate cancellation, update lawyer status
      if (!cancelAtPeriodEnd) {
        const subscription = await this.getSubscription(subscriptionId);
        if (subscription) {
          await this.updateLawyerSubscriptionStatus(subscription.lawyerId, 'cancelled');
        }
      }

      return true;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return false;
    }
  }

  // Reactivate subscription
  static async reactivateSubscription(subscriptionId: string): Promise<boolean> {
    try {
      await updateDoc(doc(db, 'lawyerSubscriptions', subscriptionId), {
        status: 'active',
        cancelAtPeriodEnd: false,
        suspendedAt: null,
        updatedAt: serverTimestamp()
      });

      const subscription = await this.getSubscription(subscriptionId);
      if (subscription) {
        await this.updateLawyerSubscriptionStatus(subscription.lawyerId, 'active');
      }

      return true;
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      return false;
    }
  }

  // Get subscription statistics
  static async getSubscriptionStats(): Promise<{
    totalActive: number;
    totalRevenue: number;
    newThisMonth: number;
    churnRate: number;
  }> {
    try {
      const subscriptionsSnapshot = await getDocs(collection(db, 'lawyerSubscriptions'));
      const subscriptions = subscriptionsSnapshot.docs.map(doc => doc.data()) as LawyerSubscription[];

      const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
      const totalRevenue = activeSubscriptions.reduce((sum, s) => sum + s.amount, 0);

      const currentMonth = new Date();
      currentMonth.setDate(1);
      const newThisMonth = subscriptions.filter(s => 
        new Date(s.createdAt) >= currentMonth && s.status === 'active'
      ).length;

      // Simple churn rate calculation (cancelled this month / total active)
      const cancelledThisMonth = subscriptions.filter(s => 
        s.cancelledAt && new Date(s.cancelledAt) >= currentMonth
      ).length;
      const churnRate = activeSubscriptions.length > 0 
        ? (cancelledThisMonth / activeSubscriptions.length) * 100 
        : 0;

      return {
        totalActive: activeSubscriptions.length,
        totalRevenue,
        newThisMonth,
        churnRate
      };
    } catch (error) {
      console.error('Error getting subscription stats:', error);
      return {
        totalActive: 0,
        totalRevenue: 0,
        newThisMonth: 0,
        churnRate: 0
      };
    }
  }

  // Accept terms and conditions
  static async acceptTerms(lawyerId: string, termsVersion: string = '1.0'): Promise<void> {
    try {
      await updateDoc(doc(db, 'lawyers', lawyerId), {
        termsAccepted: true,
        termsAcceptedAt: serverTimestamp(),
        termsVersion,
        updatedAt: serverTimestamp()
      });

      await updateDoc(doc(db, 'users', lawyerId), {
        termsAccepted: true,
        termsAcceptedAt: serverTimestamp(),
        termsVersion,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error accepting terms:', error);
      throw error;
    }
  }

  // Check if lawyer has accepted terms
  static async hasAcceptedTerms(lawyerId: string): Promise<boolean> {
    try {
      const lawyerDoc = await getDoc(doc(db, 'lawyers', lawyerId));
      if (lawyerDoc.exists()) {
        const data = lawyerDoc.data();
        return data.termsAccepted === true;
      }
      return false;
    } catch (error) {
      console.error('Error checking terms acceptance:', error);
      return false;
    }
  }
}
