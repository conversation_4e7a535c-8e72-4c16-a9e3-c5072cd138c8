rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Helper function to check if user owns the document
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    // Users can read and write their own user document
    match /users/{userId} {
      // Allow authenticated users to read and write their own document
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow user creation during registration (when document doesn't exist yet)
      allow create: if request.auth != null &&
        request.auth.uid == userId &&
        request.resource.data.id == request.auth.uid &&
        request.resource.data.email == request.auth.token.email;
    }
    
    // Lawyers collection - public read, authenticated write for own profile
    match /lawyers/{lawyerId} {
      allow read: if true; // Public profiles for search

      // Allow lawyers to create their own profile
      allow create: if request.auth != null &&
        request.auth.uid == lawyerId &&
        request.resource.data.id == request.auth.uid;

      // Allow lawyers to update their own profile or admin to update any
      allow update, delete: if request.auth != null &&
        (request.auth.uid == lawyerId ||
         (resource.data.role != null &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'));
    }
    
    // Appointments - only involved parties can access
    match /appointments/{appointmentId} {
      // Allow creation by authenticated users
      allow create: if request.auth != null &&
        (request.auth.uid == request.resource.data.clientId ||
         request.auth.uid == request.resource.data.lawyerId);

      // Allow read/update by involved parties or admin
      allow read, update: if request.auth != null &&
        (request.auth.uid == resource.data.clientId ||
         request.auth.uid == resource.data.lawyerId ||
         isAdmin());

      // Allow delete by admin only
      allow delete: if isAdmin();
    }
    
    // Reviews - public read, authenticated write
    match /reviews/{reviewId} {
      allow read: if true;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.clientId;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.clientId ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Messages - only conversation participants
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      
      match /messages/{messageId} {
        allow read, write: if request.auth != null && 
          request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
      }
    }
    
    // Admin only collections
    match /admin/{document=**} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Legal categories - public read, admin write
    match /legalCategories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
