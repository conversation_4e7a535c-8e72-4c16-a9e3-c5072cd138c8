# 💳 Payment System Setup Guide

## 🚀 Complete Stripe Integration with Firebase

This guide will help you set up the complete payment system for your legal platform.

## 📋 Prerequisites

1. **Stripe Account**: Create a Stripe account at [stripe.com](https://stripe.com)
2. **Firebase Project**: Ensure your Firebase project is set up
3. **Environment Variables**: Configure your Stripe keys

## 🔧 Environment Configuration

Add these variables to your `.env.local` file:

```bash
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

## 🏗️ Stripe Dashboard Setup

### 1. Get Your API Keys
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers > API keys**
3. Copy your **Publishable key** and **Secret key**
4. Use test keys for development (they start with `pk_test_` and `sk_test_`)

### 2. Set Up Webhooks
1. Go to **Developers > Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL: `https://yourdomain.com/api/payments/webhook`
4. Select these events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `payment_intent.canceled`
   - `charge.dispute.created`
   - `invoice.payment_succeeded`
5. Copy the **Signing secret** (starts with `whsec_`)

## 🔥 Firebase Collections Structure

The payment system creates these collections:

### `payments` Collection
```javascript
{
  id: "payment_id",
  clientId: "user_id",
  lawyerId: "lawyer_id", 
  appointmentId: "appointment_id", // optional
  amount: 150.00,
  currency: "usd",
  description: "Legal consultation",
  status: "succeeded", // pending, processing, succeeded, failed, cancelled, refunded
  paymentIntentId: "pi_stripe_id",
  platformFee: 7.50, // 5% platform fee
  lawyerAmount: 142.50, // amount after platform fee
  receiptUrl: "https://stripe.com/receipt",
  paymentMethod: "card",
  metadata: {
    lawyerName: "John Doe",
    clientName: "Jane Smith",
    appointmentDate: "2024-01-15",
    appointmentTime: "10:00 AM",
    specialty: "Family Law"
  },
  createdAt: "2024-01-01T10:00:00Z",
  updatedAt: "2024-01-01T10:05:00Z",
  paidAt: "2024-01-01T10:05:00Z"
}
```

### `lawyerSettings` Collection
```javascript
{
  userId: "lawyer_id",
  notifications: {
    email: true,
    push: true,
    paymentUpdates: true
  },
  business: {
    bufferTime: 15,
    maxAdvanceBooking: 30,
    cancellationPolicy: 24
  }
}
```

## 🎨 Mobile-Responsive Components

### Payment Form Features
- ✅ **Mobile-first design** with responsive breakpoints
- ✅ **Touch-friendly** buttons and inputs
- ✅ **Secure card input** with Stripe Elements
- ✅ **Real-time validation** and error handling
- ✅ **Loading states** and success animations
- ✅ **Platform fee transparency**

### Payment Modal Features
- ✅ **Full-screen on mobile** for better UX
- ✅ **Backdrop blur** for modern aesthetics
- ✅ **Swipe gestures** support
- ✅ **Auto-focus** on card input
- ✅ **Keyboard navigation** support

## 💰 Payment Flow

### 1. Client Books Appointment
```javascript
// Client clicks "Book Consultation"
handleBookConsultation() {
  // 1. Create appointment record
  // 2. Open payment modal
  // 3. Process payment with Stripe
  // 4. Update appointment status
  // 5. Send notifications
}
```

### 2. Payment Processing
```javascript
// Payment intent creation
POST /api/payments/create-payment-intent
{
  amount: 150,
  clientId: "client_id",
  lawyerId: "lawyer_id",
  description: "Legal consultation"
}

// Returns client_secret for Stripe
{
  clientSecret: "pi_xxx_secret_xxx",
  paymentId: "firestore_payment_id"
}
```

### 3. Webhook Processing
```javascript
// Stripe webhook handles payment events
POST /api/payments/webhook
{
  type: "payment_intent.succeeded",
  data: { /* payment data */ }
}

// Updates Firebase and sends notifications
```

## 📱 Mobile Optimization

### Responsive Design
- **Breakpoints**: `sm:`, `md:`, `lg:`, `xl:`
- **Touch targets**: Minimum 44px for buttons
- **Font sizes**: Scalable from 14px to 18px
- **Spacing**: Consistent padding/margins

### Performance
- **Lazy loading** for payment components
- **Code splitting** for Stripe libraries
- **Optimized images** and icons
- **Minimal bundle size**

## 🔒 Security Features

### Payment Security
- ✅ **PCI DSS Compliance** via Stripe
- ✅ **SSL/TLS Encryption** for all requests
- ✅ **Webhook signature verification**
- ✅ **No card data storage** on your servers

### Firebase Security Rules
```javascript
// payments collection rules
match /payments/{paymentId} {
  allow read, write: if request.auth != null && 
    (resource.data.clientId == request.auth.uid || 
     resource.data.lawyerId == request.auth.uid);
}
```

## 🧪 Testing

### Test Cards
Use these Stripe test cards:

- **Success**: `****************`
- **Decline**: `****************`
- **3D Secure**: `****************`
- **Insufficient funds**: `****************`

### Test Flow
1. Use test API keys
2. Create test appointments
3. Process test payments
4. Verify webhook delivery
5. Check Firebase data

## 🚀 Production Deployment

### 1. Switch to Live Keys
- Replace test keys with live keys
- Update webhook endpoint to production URL
- Enable live mode in Stripe dashboard

### 2. Webhook Configuration
- Set production webhook URL
- Verify SSL certificate
- Test webhook delivery

### 3. Firebase Security
- Update security rules
- Enable audit logging
- Set up monitoring

## 📊 Analytics & Monitoring

### Payment Metrics
- **Success rate**: Track payment completion
- **Average transaction**: Monitor payment amounts
- **Platform revenue**: Calculate fees collected
- **Refund rate**: Track refund requests

### Error Monitoring
- **Failed payments**: Log and alert on failures
- **Webhook failures**: Monitor webhook delivery
- **API errors**: Track API response codes

## 🎯 Features Implemented

### ✅ Core Payment Features
- Stripe payment processing
- Firebase integration
- Webhook handling
- Payment history
- Mobile-responsive UI

### ✅ Business Logic
- Platform fee calculation (5%)
- Lawyer earnings tracking
- Payment notifications
- Refund processing
- Dispute handling

### ✅ User Experience
- Clean, modern UI
- Mobile-first design
- Real-time updates
- Error handling
- Success animations

## 🔧 Customization

### Platform Fee
Change the platform fee in `PaymentService`:
```javascript
static calculatePlatformFee(amount: number, feePercentage: number = 0.05)
```

### Styling
Customize colors and design in:
- `components/payments/payment-form.tsx`
- `components/payments/payment-modal.tsx`
- Tailwind CSS classes

### Notifications
Modify notification templates in:
- `services/notification-service.ts`
- `services/payment-service.ts`

## 🆘 Troubleshooting

### Common Issues
1. **Webhook not receiving events**: Check URL and SSL
2. **Payment failing**: Verify API keys and test cards
3. **Firebase permission denied**: Check security rules
4. **Mobile UI issues**: Test on real devices

### Debug Mode
Enable debug logging:
```javascript
// In payment components
console.log('Payment debug:', { amount, clientSecret });
```

## 📞 Support

For issues with:
- **Stripe**: [Stripe Support](https://support.stripe.com)
- **Firebase**: [Firebase Support](https://firebase.google.com/support)
- **Implementation**: Check console logs and network tab

---

**🎉 Your payment system is now ready for production!**

The system handles everything from payment processing to mobile optimization, ensuring a smooth experience for both lawyers and clients.
