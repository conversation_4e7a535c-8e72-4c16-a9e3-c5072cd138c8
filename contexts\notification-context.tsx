'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  onSnapshot, 
  doc, 
  updateDoc, 
  addDoc, 
  deleteDoc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/auth-context';
import { 
  Notification, 
  NotificationType, 
  UserNotificationSettings, 
  NOTIFICATION_TEMPLATES 
} from '@/types/notifications';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  createNotification: (
    userId: string,
    type: NotificationType,
    data?: Record<string, any>,
    customTitle?: string,
    customMessage?: string
  ) => Promise<void>;
  getNotificationSettings: () => Promise<UserNotificationSettings | null>;
  updateNotificationSettings: (settings: Partial<UserNotificationSettings>) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Real-time notifications listener
  useEffect(() => {
    if (!user || !db) {
      setNotifications([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    const notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', user.id),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(
      notificationsQuery,
      (snapshot) => {
        const notificationsList: Notification[] = [];
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          
          // Convert Firestore timestamps to ISO strings
          const createdAt = data.createdAt instanceof Timestamp 
            ? data.createdAt.toDate().toISOString()
            : data.createdAt;
          const updatedAt = data.updatedAt instanceof Timestamp 
            ? data.updatedAt.toDate().toISOString()
            : data.updatedAt;
          const expiresAt = data.expiresAt instanceof Timestamp 
            ? data.expiresAt.toDate().toISOString()
            : data.expiresAt;

          notificationsList.push({
            id: doc.id,
            ...data,
            createdAt,
            updatedAt,
            expiresAt
          } as Notification);
        });

        // Filter out expired notifications
        const activeNotifications = notificationsList.filter(notification => {
          if (!notification.expiresAt) return true;
          return new Date(notification.expiresAt) > new Date();
        });

        setNotifications(activeNotifications);
        setIsLoading(false);
      },
      (error) => {
        console.error('Error fetching notifications:', error);
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [user]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = async (notificationId: string) => {
    try {
      await updateDoc(doc(db, 'notifications', notificationId), {
        read: true,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.read);
      
      const updatePromises = unreadNotifications.map(notification =>
        updateDoc(doc(db, 'notifications', notification.id), {
          read: true,
          updatedAt: serverTimestamp()
        })
      );

      await Promise.all(updatePromises);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      await deleteDoc(doc(db, 'notifications', notificationId));
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const createNotification = async (
    userId: string,
    type: NotificationType,
    data: Record<string, any> = {},
    customTitle?: string,
    customMessage?: string
  ) => {
    try {
      const template = NOTIFICATION_TEMPLATES[type];
      if (!template) {
        console.error('Unknown notification type:', type);
        return;
      }

      // Replace variables in title and message
      let title = customTitle || template.title;
      let message = customMessage || template.message;

      template.variables.forEach(variable => {
        if (data[variable]) {
          title = title.replace(`{${variable}}`, data[variable]);
          message = message.replace(`{${variable}}`, data[variable]);
        }
      });

      const notification: Omit<Notification, 'id'> = {
        userId,
        type,
        title,
        message,
        data,
        read: false,
        priority: template.priority,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        actionUrl: data.actionUrl,
        actionText: template.actionText
      };

      // Set expiration for certain types of notifications
      if (['appointment_reminder', 'system_maintenance'].includes(type)) {
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 7); // Expire in 7 days
        notification.expiresAt = expirationDate.toISOString();
      }

      await addDoc(collection(db, 'notifications'), {
        ...notification,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        ...(notification.expiresAt && { expiresAt: new Date(notification.expiresAt) })
      });

    } catch (error) {
      console.error('Error creating notification:', error);
    }
  };

  const getNotificationSettings = async (): Promise<UserNotificationSettings | null> => {
    if (!user) return null;

    try {
      const settingsDoc = await import('firebase/firestore').then(({ getDoc, doc }) => 
        getDoc(doc(db, 'notificationSettings', user.id))
      );

      if (settingsDoc.exists()) {
        return settingsDoc.data() as UserNotificationSettings;
      }

      // Return default settings if none exist
      const defaultSettings: UserNotificationSettings = {
        userId: user.id,
        preferences: {},
        globalSettings: {
          email: true,
          push: true,
          sms: false,
          inApp: true,
          quietHours: {
            enabled: false,
            start: '22:00',
            end: '08:00'
          }
        },
        updatedAt: new Date().toISOString()
      };

      return defaultSettings;
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return null;
    }
  };

  const updateNotificationSettings = async (settings: Partial<UserNotificationSettings>) => {
    if (!user) return;

    try {
      const { setDoc } = await import('firebase/firestore');
      
      await setDoc(doc(db, 'notificationSettings', user.id), {
        ...settings,
        userId: user.id,
        updatedAt: serverTimestamp()
      }, { merge: true });

    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  };

  const value = {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    createNotification,
    getNotificationSettings,
    updateNotificationSettings
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}
