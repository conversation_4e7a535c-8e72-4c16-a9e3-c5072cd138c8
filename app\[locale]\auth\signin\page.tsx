import { Metadata } from 'next';
import { setRequestLocale } from 'next-intl/server';
import LoginForm from '@/components/auth/login-form';

// Force dynamic rendering to prevent Firebase initialization during build
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Iniciar <PERSON>',
  description: 'Inicia sesión en tu cuenta de Abogo para acceder a servicios legales en Puerto Rico',
};

interface SignInPageProps {
  params: { locale: string };
}

export default function SignInPage({ params: { locale } }: SignInPageProps) {
  // Enable static rendering
  setRequestLocale(locale);
  
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Unified Background */}
      <div className="fixed inset-0 -z-10">
        {/* Main Unified Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900"></div>
        
        {/* Secondary Gradient Overlay for Depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        
        {/* Subtle Grid Pattern Overlay */}
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        
        {/* Gradient Accent Areas */}
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-r from-transparent via-yellow-500/2 to-transparent"></div>
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-yellow-500/5 to-yellow-600/5 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-float"></div>
        <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-yellow-600/5 to-amber-700/5 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 w-72 h-72 bg-gradient-to-r from-orange-500/5 to-yellow-500/5 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 shadow-2xl">
          <LoginForm />
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 border border-yellow-500/20 rounded-full"></div>
      <div className="absolute bottom-10 right-10 w-16 h-16 border border-yellow-500/20 rounded-full"></div>
      <div className="absolute top-1/2 left-10 w-2 h-2 bg-yellow-500/50 rounded-full"></div>
      <div className="absolute top-1/4 right-20 w-2 h-2 bg-yellow-500/50 rounded-full"></div>
    </div>
  );
}

export function generateStaticParams() {
  return [{ locale: 'en' }, { locale: 'es' }];
}
