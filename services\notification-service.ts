import { 
  collection, 
  addDoc, 
  serverTimestamp, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { NotificationType, NOTIFICATION_TEMPLATES } from '@/types/notifications';

export class NotificationService {
  
  // Create a notification for a specific user
  static async createNotification(
    userId: string,
    type: NotificationType,
    data: Record<string, any> = {},
    customTitle?: string,
    customMessage?: string
  ) {
    try {
      const template = NOTIFICATION_TEMPLATES[type];
      if (!template) {
        console.error('Unknown notification type:', type);
        return;
      }

      // Replace variables in title and message
      let title = customTitle || template.title;
      let message = customMessage || template.message;

      template.variables.forEach(variable => {
        if (data[variable]) {
          title = title.replace(`{${variable}}`, data[variable]);
          message = message.replace(`{${variable}}`, data[variable]);
        }
      });

      const notification = {
        userId,
        type,
        title,
        message,
        data,
        read: false,
        priority: template.priority,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        actionUrl: data.actionUrl,
        actionText: template.actionText
      };

      // Set expiration for certain types of notifications
      if (['appointment_reminder', 'system_maintenance'].includes(type)) {
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 7);
        notification.expiresAt = expirationDate;
      }

      await addDoc(collection(db, 'notifications'), notification);
    } catch (error) {
      console.error('Error creating notification:', error);
    }
  }

  // Appointment-related notifications
  static async notifyAppointmentRequest(lawyerId: string, clientName: string, appointmentData: any) {
    await this.createNotification(lawyerId, 'appointment_request', {
      clientName,
      specialty: appointmentData.specialty,
      date: appointmentData.date,
      time: appointmentData.time,
      actionUrl: `/es/dashboard/lawyer/appointments/${appointmentData.id}`
    });
  }

  static async notifyAppointmentConfirmed(clientId: string, lawyerName: string, appointmentData: any) {
    await this.createNotification(clientId, 'appointment_confirmed', {
      lawyerName,
      date: appointmentData.date,
      time: appointmentData.time,
      actionUrl: `/es/dashboard/client/appointments/${appointmentData.id}`
    });
  }

  static async notifyAppointmentCancelled(userId: string, otherPartyName: string, appointmentData: any) {
    await this.createNotification(userId, 'appointment_cancelled', {
      lawyerName: otherPartyName,
      date: appointmentData.date,
      reason: appointmentData.cancellationReason || 'No especificado',
      actionUrl: `/es/lawyers`
    });
  }

  static async notifyAppointmentReminder(userId: string, otherPartyName: string, appointmentData: any) {
    await this.createNotification(userId, 'appointment_reminder', {
      lawyerName: otherPartyName,
      time: appointmentData.time,
      location: appointmentData.location || 'Virtual',
      actionUrl: `/es/dashboard/client/appointments/${appointmentData.id}`
    });
  }

  static async notifyAppointmentCompleted(clientId: string, lawyerName: string, appointmentData: any) {
    await this.createNotification(clientId, 'appointment_completed', {
      lawyerName,
      actionUrl: `/es/lawyers/${appointmentData.lawyerId}/review`
    });
  }

  // Message notifications
  static async notifyNewMessage(recipientId: string, senderName: string, messageData: any) {
    await this.createNotification(recipientId, 'new_message', {
      senderName,
      preview: messageData.content?.substring(0, 50) + '...',
      actionUrl: `/es/dashboard/messages/${messageData.conversationId}`
    });
  }

  static async notifyMessageReply(recipientId: string, senderName: string, messageData: any) {
    await this.createNotification(recipientId, 'message_reply', {
      senderName,
      preview: messageData.content?.substring(0, 50) + '...',
      actionUrl: `/es/dashboard/messages/${messageData.conversationId}`
    });
  }

  // Payment notifications
  static async notifyPaymentReceived(lawyerId: string, clientName: string, paymentData: any) {
    await this.createNotification(lawyerId, 'payment_received', {
      amount: paymentData.amount,
      clientName,
      service: paymentData.service,
      actionUrl: `/es/dashboard/lawyer/earnings`
    });
  }

  static async notifyPaymentPending(clientId: string, paymentData: any) {
    await this.createNotification(clientId, 'payment_pending', {
      amount: paymentData.amount,
      service: paymentData.service,
      dueDate: paymentData.dueDate,
      actionUrl: `/es/dashboard/client/payments/${paymentData.id}`
    });
  }

  static async notifyPaymentFailed(clientId: string, paymentData: any) {
    await this.createNotification(clientId, 'payment_failed', {
      amount: paymentData.amount,
      reason: paymentData.failureReason,
      actionUrl: `/es/dashboard/client/payments/${paymentData.id}`
    });
  }

  // Review notifications
  static async notifyNewReview(lawyerId: string, clientName: string, reviewData: any) {
    await this.createNotification(lawyerId, 'new_review', {
      clientName,
      rating: reviewData.rating,
      comment: reviewData.comment?.substring(0, 100) + '...',
      actionUrl: `/es/dashboard/lawyer/reviews`
    });
  }

  // Profile notifications
  static async notifyProfileIncomplete(userId: string, completionPercentage: number) {
    await this.createNotification(userId, 'profile_incomplete', {
      completionPercentage: `${completionPercentage}%`,
      actionUrl: `/es/dashboard/profile`
    });
  }

  static async notifyProfileVerified(userId: string) {
    await this.createNotification(userId, 'profile_verified', {
      actionUrl: `/es/dashboard/profile`
    });
  }

  // System notifications
  static async notifySystemMaintenance(userIds: string[], maintenanceData: any) {
    const promises = userIds.map(userId =>
      this.createNotification(userId, 'system_maintenance', {
        date: maintenanceData.date,
        startTime: maintenanceData.startTime,
        endTime: maintenanceData.endTime
      })
    );
    await Promise.all(promises);
  }

  static async notifySystemUpdate(userIds: string[], updateData: any) {
    const promises = userIds.map(userId =>
      this.createNotification(userId, 'system_update', {
        features: updateData.features,
        actionUrl: `/es/updates`
      })
    );
    await Promise.all(promises);
  }

  static async notifyAccountSecurity(userId: string, securityData: any) {
    await this.createNotification(userId, 'account_security', {
      device: securityData.device,
      location: securityData.location,
      time: securityData.time,
      actionUrl: `/es/dashboard/security`
    });
  }

  static async notifyWelcome(userId: string, firstName: string) {
    await this.createNotification(userId, 'welcome', {
      firstName,
      actionUrl: `/es/dashboard`
    });
  }

  // Lawyer-specific notifications
  static async notifyNewClientInquiry(lawyerId: string, clientName: string, inquiryData: any) {
    await this.createNotification(lawyerId, 'new_client_inquiry', {
      clientName,
      specialty: inquiryData.specialty,
      message: inquiryData.message?.substring(0, 100) + '...',
      actionUrl: `/es/dashboard/lawyer/inquiries/${inquiryData.id}`
    });
  }

  static async notifyClientBookingRequest(lawyerId: string, clientName: string, bookingData: any) {
    await this.createNotification(lawyerId, 'client_booking_request', {
      clientName,
      preferredDate: bookingData.preferredDate,
      preferredTime: bookingData.preferredTime,
      actionUrl: `/es/dashboard/lawyer/bookings/${bookingData.id}`
    });
  }

  static async notifyAvailabilityReminder(lawyerId: string, daysSinceUpdate: number) {
    await this.createNotification(lawyerId, 'availability_reminder', {
      days: daysSinceUpdate.toString(),
      actionUrl: `/es/dashboard/lawyer/availability`
    });
  }

  static async notifyEarningsSummary(lawyerId: string, earningsData: any) {
    await this.createNotification(lawyerId, 'earnings_summary', {
      amount: earningsData.amount,
      appointments: earningsData.appointments.toString(),
      period: earningsData.period,
      actionUrl: `/es/dashboard/lawyer/earnings`
    });
  }

  // Client-specific notifications
  static async notifyLawyerResponse(clientId: string, lawyerName: string, responseData: any) {
    await this.createNotification(clientId, 'lawyer_response', {
      lawyerName,
      actionUrl: `/es/dashboard/client/inquiries/${responseData.inquiryId}`
    });
  }

  static async notifyConsultationAvailable(clientId: string, lawyerName: string, availabilityData: any) {
    await this.createNotification(clientId, 'consultation_available', {
      lawyerName,
      availableDates: availabilityData.dates.join(', '),
      actionUrl: `/es/lawyers/${availabilityData.lawyerId}`
    });
  }

  static async notifyDocumentReady(clientId: string, documentData: any) {
    await this.createNotification(clientId, 'document_ready', {
      documentType: documentData.type,
      lawyerName: documentData.lawyerName,
      actionUrl: `/es/dashboard/client/documents/${documentData.id}`
    });
  }

  // Bulk notifications
  static async sendBulkNotification(
    userIds: string[],
    type: NotificationType,
    data: Record<string, any> = {}
  ) {
    const promises = userIds.map(userId => this.createNotification(userId, type, data));
    await Promise.all(promises);
  }

  // Get all users for system-wide notifications
  static async getAllUserIds(): Promise<string[]> {
    try {
      const usersSnapshot = await getDocs(collection(db, 'users'));
      return usersSnapshot.docs.map(doc => doc.id);
    } catch (error) {
      console.error('Error getting all user IDs:', error);
      return [];
    }
  }

  // Get users by role
  static async getUserIdsByRole(role: 'client' | 'lawyer'): Promise<string[]> {
    try {
      const usersQuery = query(collection(db, 'users'), where('role', '==', role));
      const usersSnapshot = await getDocs(usersQuery);
      return usersSnapshot.docs.map(doc => doc.id);
    } catch (error) {
      console.error('Error getting user IDs by role:', error);
      return [];
    }
  }

  // Subscription-related notifications
  static async notifySubscriptionActivated(
    lawyerId: string,
    options: { plan: string; nextBillingDate: string }
  ): Promise<void> {
    await this.createNotification(lawyerId, 'subscription_activated', {
      plan: options.plan,
      nextBillingDate: new Date(options.nextBillingDate).toLocaleDateString('es-ES'),
      actionUrl: '/es/dashboard/lawyer'
    });
  }

  static async notifySubscriptionSuspended(
    lawyerId: string,
    options: { reason: string; reactivationUrl?: string }
  ): Promise<void> {
    await this.createNotification(lawyerId, 'subscription_suspended', {
      reason: options.reason,
      actionUrl: options.reactivationUrl || '/es/dashboard/lawyer/subscription'
    });
  }

  static async notifySubscriptionCancelled(
    lawyerId: string,
    options: { endDate: string }
  ): Promise<void> {
    await this.createNotification(lawyerId, 'subscription_cancelled', {
      endDate: new Date(options.endDate).toLocaleDateString('es-ES'),
      actionUrl: '/es/dashboard/lawyer/subscription'
    });
  }
}
