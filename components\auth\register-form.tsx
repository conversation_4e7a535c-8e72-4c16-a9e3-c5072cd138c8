'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, Mail, Lock, Phone, Eye, EyeOff, Scale, ArrowRight, Users, Briefcase } from 'lucide-react';
import Link from 'next/link';

export default function RegisterForm() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    userType: 'client' as 'client' | 'lawyer'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { register } = useAuth();
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleUserTypeChange = (value: string) => {
    setFormData({
      ...formData,
      userType: value as 'client' | 'lawyer'
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError('Las contraseñas no coinciden');
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setError('La contraseña debe tener al menos 6 caracteres');
      setIsLoading(false);
      return;
    }

    try {
      const result = await register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        password: formData.password,
        userType: formData.userType,
      });
      
      if (result.success) {
        // Registration successful - redirect will be handled by auth context
        router.push('/dashboard');
      } else {
        setError(result.error || 'Error al crear la cuenta');
      }
    } catch (error: any) {
      setError('Error al crear la cuenta. Por favor intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Header with Logo */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-6">
          <div className="relative">
            <Scale className="h-12 w-12 text-yellow-500" />
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <span className="text-xs font-bold text-black">PR</span>
            </div>
          </div>
          <div className="ml-3">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 bg-clip-text text-transparent">
              Abogo
            </h1>
            <p className="text-xs text-gray-400 -mt-1">Puerto Rico Legal</p>
          </div>
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">Únete a nuestra comunidad</h2>
        <p className="text-gray-400">Crea tu cuenta para comenzar</p>
      </div>

      {/* User Type Selection */}
      <div className="mb-8">
        <Label className="text-white font-medium mb-4 block">¿Cómo te unirás a Abogo?</Label>
        <div className="grid grid-cols-2 gap-4">
          <button
            type="button"
            onClick={() => handleUserTypeChange('client')}
            className={`p-6 rounded-xl border-2 transition-all duration-300 ${
              formData.userType === 'client'
                ? 'border-yellow-500 bg-yellow-500/10 backdrop-blur-sm'
                : 'border-white/20 bg-white/5 hover:border-white/30 backdrop-blur-sm'
            }`}
          >
            <Users className={`h-8 w-8 mx-auto mb-3 ${
              formData.userType === 'client' ? 'text-yellow-500' : 'text-gray-400'
            }`} />
            <h3 className={`font-semibold mb-2 ${
              formData.userType === 'client' ? 'text-yellow-500' : 'text-white'
            }`}>
              Busco ayuda legal
            </h3>
            <p className="text-sm text-gray-400">
              Necesito encontrar un abogado para mi caso
            </p>
          </button>
          
          <button
            type="button"
            onClick={() => handleUserTypeChange('lawyer')}
            className={`p-6 rounded-xl border-2 transition-all duration-300 ${
              formData.userType === 'lawyer'
                ? 'border-yellow-500 bg-yellow-500/10 backdrop-blur-sm'
                : 'border-white/20 bg-white/5 hover:border-white/30 backdrop-blur-sm'
            }`}
          >
            <Briefcase className={`h-8 w-8 mx-auto mb-3 ${
              formData.userType === 'lawyer' ? 'text-yellow-500' : 'text-gray-400'
            }`} />
            <h3 className={`font-semibold mb-2 ${
              formData.userType === 'lawyer' ? 'text-yellow-500' : 'text-white'
            }`}>
              Soy abogado
            </h3>
            <p className="text-sm text-gray-400">
              Quiero ofrecer mis servicios legales
            </p>
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-xl backdrop-blur-sm">
          <p className="text-red-400 text-sm text-center">{error}</p>
        </div>
      )}

      {/* Registration Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-white font-medium">
              Nombre
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              <Input
                id="firstName"
                name="firstName"
                type="text"
                required
                value={formData.firstName}
                onChange={handleChange}
                placeholder="Juan"
                className="pl-12 h-12 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm"
                disabled={isLoading}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-white font-medium">
              Apellido
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              <Input
                id="lastName"
                name="lastName"
                type="text"
                required
                value={formData.lastName}
                onChange={handleChange}
                placeholder="Pérez"
                className="pl-12 h-12 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm"
                disabled={isLoading}
              />
            </div>
          </div>
        </div>

        {/* Email Field */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-white font-medium">
            Correo Electrónico
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              className="pl-12 h-12 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm"
              disabled={isLoading}
            />
          </div>
        </div>

        {/* Phone Field */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="text-white font-medium">
            Teléfono
          </Label>
          <div className="relative">
            <Phone className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
            <Input
              id="phone"
              name="phone"
              type="tel"
              required
              value={formData.phone}
              onChange={handleChange}
              placeholder="(*************"
              className="pl-12 h-12 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm"
              disabled={isLoading}
            />
          </div>
        </div>

        {/* Password Fields */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="password" className="text-white font-medium">
              Contraseña
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              <Input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                required
                value={formData.password}
                onChange={handleChange}
                placeholder="••••••••"
                className="pl-12 pr-12 h-12 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm"
                disabled={isLoading}
              />
              <button
                type="button"
                className="absolute right-3 top-3.5 text-gray-400 hover:text-white transition-colors"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-white font-medium">
              Confirmar Contraseña
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                required
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="••••••••"
                className="pl-12 pr-12 h-12 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm"
                disabled={isLoading}
              />
              <button
                type="button"
                className="absolute right-3 top-3.5 text-gray-400 hover:text-white transition-colors"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
          </div>
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 hover:from-yellow-500 hover:via-orange-600 hover:to-yellow-500 text-black font-semibold rounded-xl shadow-lg hover:shadow-yellow-500/25 transition-all duration-300 hover:scale-[1.02] group"
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
              Creando cuenta...
            </div>
          ) : (
            <div className="flex items-center">
              Crear Cuenta
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </div>
          )}
        </Button>
      </form>

      {/* Login Link */}
      <div className="mt-8 text-center">
        <p className="text-gray-400">
          ¿Ya tienes cuenta?{' '}
          <Link
            href="/auth/signin"
            className="text-yellow-500 hover:text-yellow-400 font-medium transition-colors"
          >
            Inicia sesión aquí
          </Link>
        </p>
      </div>
    </div>
  );
}
