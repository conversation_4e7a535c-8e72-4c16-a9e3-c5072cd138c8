'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  MessageSquare, 
  Send,
  Search,
  Scale,
  Clock,
  CheckCircle2
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function ClientMessages() {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadConversations();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const loadConversations = async () => {
    try {
      setLoading(true);
      
      const messagesQuery = query(
        collection(db, 'messages'),
        where('clientId', '==', user.id),
        orderBy('timestamp', 'desc')
      );
      
      const messagesSnapshot = await getDocs(messagesQuery);
      const messagesData = messagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // Group messages by lawyer
      const conversationsMap = {};
      messagesData.forEach(message => {
        const lawyerId = message.lawyerId;
        if (!conversationsMap[lawyerId]) {
          conversationsMap[lawyerId] = {
            lawyerId,
            lawyerName: message.lawyerName,
            lastMessage: message,
            messages: []
          };
        }
        conversationsMap[lawyerId].messages.push(message);
      });
      
      setConversations(Object.values(conversationsMap));
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectConversation = (conversation) => {
    setSelectedConversation(conversation);
    setMessages(conversation.messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)));
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return;
    
    // Here you would implement the actual message sending logic
    console.log('Sending message:', newMessage);
    setNewMessage('');
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white flex items-center">
          <MessageSquare className="mr-3 h-8 w-8 text-yellow-500" />
          Mensajes
        </h1>
        <p className="mt-2 text-gray-400">
          Comunícate directamente con tus abogados
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        {/* Conversations List */}
        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">Conversaciones</CardTitle>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar conversaciones..."
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="space-y-1">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto"></div>
                  <p className="text-gray-400 mt-2">Cargando...</p>
                </div>
              ) : conversations.length > 0 ? (
                conversations.map((conversation) => (
                  <button
                    key={conversation.lawyerId}
                    onClick={() => selectConversation(conversation)}
                    className={`w-full p-4 text-left hover:bg-white/10 transition-colors border-b border-white/10 ${
                      selectedConversation?.lawyerId === conversation.lawyerId ? 'bg-white/10' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="w-10 h-10 bg-yellow-500/20 rounded-full flex items-center justify-center">
                        <Scale className="h-5 w-5 text-yellow-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-white truncate">
                          {conversation.lawyerName}
                        </p>
                        <p className="text-xs text-gray-400 truncate">
                          {conversation.lastMessage.content || 'Nuevo mensaje'}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {conversation.lastMessage.timestamp || 'Reciente'}
                        </p>
                      </div>
                    </div>
                  </button>
                ))
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">No tienes conversaciones</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Messages Area */}
        <div className="lg:col-span-2">
          {selectedConversation ? (
            <Card className="bg-white/5 border-white/10 backdrop-blur-sm h-full flex flex-col">
              {/* Chat Header */}
              <CardHeader className="border-b border-white/10">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-yellow-500/20 rounded-full flex items-center justify-center">
                    <Scale className="h-5 w-5 text-yellow-500" />
                  </div>
                  <div>
                    <CardTitle className="text-white">{selectedConversation.lawyerName}</CardTitle>
                    <CardDescription className="text-gray-400">Abogado</CardDescription>
                  </div>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.length > 0 ? (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.senderId === user.id ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.senderId === user.id
                            ? 'bg-yellow-500 text-black'
                            : 'bg-white/10 text-white'
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                        <div className="flex items-center justify-between mt-1">
                          <p className={`text-xs ${
                            message.senderId === user.id ? 'text-black/70' : 'text-gray-400'
                          }`}>
                            {message.timestamp || 'Ahora'}
                          </p>
                          {message.senderId === user.id && (
                            <CheckCircle2 className="h-3 w-3 text-black/70" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">No hay mensajes en esta conversación</p>
                  </div>
                )}
              </CardContent>

              {/* Message Input */}
              <div className="border-t border-white/10 p-4">
                <div className="flex space-x-2">
                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Escribe tu mensaje..."
                    className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  />
                  <Button
                    onClick={sendMessage}
                    className="bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 hover:from-yellow-500 hover:via-orange-600 hover:to-yellow-500 text-black"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ) : (
            <Card className="bg-white/5 border-white/10 backdrop-blur-sm h-full flex items-center justify-center">
              <div className="text-center">
                <MessageSquare className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Selecciona una conversación</h3>
                <p className="text-gray-400">
                  Elige una conversación de la lista para ver los mensajes
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
