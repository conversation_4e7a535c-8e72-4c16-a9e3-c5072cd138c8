'use client';

import React from 'react';
import { useAuth } from '@/contexts/auth-context';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { ClientDashboard } from '@/components/dashboard/client-dashboard';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function ClientDashboardPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && user) {
      // Redirect lawyers to their dashboard
      if (user.role === 'lawyer') {
        router.push('/dashboard/lawyer');
        return;
      }
      // Redirect admins to their dashboard (we'll keep admin in the original dashboard)
      if (user.role === 'admin') {
        router.push('/dashboard');
        return;
      }
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!user) {
    // Redirect to login will be handled by middleware
    return null;
  }

  // Only show client dashboard for clients
  if (user.role !== 'client') {
    return null;
  }

  return (
    <DashboardLayout>
      <ClientDashboard />
    </DashboardLayout>
  );
}
