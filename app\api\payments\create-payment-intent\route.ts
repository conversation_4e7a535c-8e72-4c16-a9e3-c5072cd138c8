import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { PaymentService } from '@/services/payment-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      amount, 
      currency = 'usd', 
      clientId, 
      lawyerId, 
      appointmentId, 
      description,
      metadata = {} 
    } = body;

    // Validate required fields
    if (!amount || !clientId || !lawyerId || !description) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Calculate platform fee (5%)
    const platformFee = PaymentService.calculatePlatformFee(amount);
    const lawyerAmount = PaymentService.calculateLawyerAmount(amount, platformFee);

    // Create payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      metadata: {
        clientId,
        lawyerId,
        appointmentId: appointmentId || '',
        platformFee: platformFee.toString(),
        lawyerAmount: lawyerAmount.toString(),
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    // Create payment record in Firestore
    const paymentId = await PaymentService.createPayment({
      clientId,
      lawyerId,
      appointmentId,
      amount,
      currency,
      description,
      status: 'pending',
      paymentIntentId: paymentIntent.id,
      platformFee,
      lawyerAmount,
      metadata: {
        ...metadata,
        stripePaymentIntentId: paymentIntent.id
      }
    });

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentId,
      amount,
      platformFee,
      lawyerAmount
    });

  } catch (error: any) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
