import { Metadata } from 'next';
import { setRequestLocale } from 'next-intl/server';
import RegisterForm from './register-form';

// Force dynamic rendering to prevent Firebase initialization during build
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Create an Account',
  description: 'Join LegalPR to find legal help or offer your legal services',
};

interface RegisterPageProps {
  params: { locale: string };
}

export default function RegisterPage({ params: { locale } }: RegisterPageProps) {
  // Enable static rendering
  setRequestLocale(locale);
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 p-4 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-yellow-500/5 to-yellow-600/5 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-float"></div>
        <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-yellow-600/5 to-amber-700/5 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="w-full max-w-2xl bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-2xl relative z-10 overflow-y-auto max-h-[90vh]">
        <RegisterForm />
      </div>
    </div>
  );
}

export function generateStaticParams() {
  return [{ locale: 'en' }, { locale: 'es' }];
}
