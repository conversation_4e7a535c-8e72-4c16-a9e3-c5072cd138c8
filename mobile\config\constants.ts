import Constants from 'expo-constants';

// Firebase Configuration
export const FIREBASE_CONFIG = {
  apiKey: "AIzaSyCh4pBG5mh5m70DfyrjJdnbfrUED04QUZA",
  authDomain: "delawpr.firebaseapp.com",
  projectId: "delawpr",
  storageBucket: "delawpr.firebasestorage.app",
  messagingSenderId: "742114936746",
  appId: "1:742114936746:web:2f73d90d67e9cc16e7cdd3",
  measurementId: "G-Q2Z0MJCLPR"
};

// Stripe Configuration
export const STRIPE_PUBLISHABLE_KEY = __DEV__ 
  ? 'pk_test_your_test_key_here' 
  : 'pk_live_your_live_key_here';

// API Configuration
export const API_BASE_URL = __DEV__
  ? 'http://localhost:3001/api'
  : 'https://delawpr.com/api';

// App Configuration
export const APP_VERSION = Constants.expoConfig?.version || '1.0.0';
export const APP_NAME = 'LegalPR';

// Legal Categories
export const LEGAL_CATEGORIES = [
  { id: 'criminal', name: 'Derecho Penal', icon: 'shield' },
  { id: 'family', name: 'Derecho de Familia', icon: 'heart' },
  { id: 'real-estate', name: 'Derecho Inmobiliario', icon: 'home' },
  { id: 'immigration', name: 'Derecho de Inmigración', icon: 'globe' },
  { id: 'corporate', name: 'Derecho Corporativo', icon: 'building' },
  { id: 'personal-injury', name: 'Lesiones Personales', icon: 'briefcase' },
  { id: 'employment', name: 'Derecho Laboral', icon: 'users' },
  { id: 'bankruptcy', name: 'Bancarrota', icon: 'dollar-sign' },
  { id: 'intellectual-property', name: 'Propiedad Intelectual', icon: 'file-text' },
  { id: 'tax', name: 'Derecho Tributario', icon: 'calculator' },
  { id: 'estate-planning', name: 'Planificación Patrimonial', icon: 'file-plus' },
  { id: 'civil-litigation', name: 'Litigios Civiles', icon: 'gavel' }
];

// Colors
export const COLORS = {
  primary: '#3b82f6',
  secondary: '#2dd4bf',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  background: '#ffffff',
  surface: '#f8fafc',
  text: '#1f2937',
  textSecondary: '#6b7280',
  border: '#e5e7eb'
};

// Notification Types
export const NOTIFICATION_TYPES = {
  APPOINTMENT: 'appointment',
  MESSAGE: 'message',
  PAYMENT: 'payment',
  REVIEW: 'review',
  SYSTEM: 'system'
};
