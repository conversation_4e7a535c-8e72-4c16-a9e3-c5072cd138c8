'use client';

import React, { useState } from 'react';
import { DashboardNav } from './dashboard-nav';
import { ClientDashboard } from './client-dashboard';
import { ClientAppointments } from './client-appointments';
import { ClientMessages } from './client-messages';
import { ClientProfile } from './client-profile';
import { ClientPayments } from './client-payments';

export function ClientDashboardLayout() {
  const [activeSection, setActiveSection] = useState('dashboard');

  const renderSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <ClientDashboard onSectionChange={setActiveSection} />;
      case 'citas':
        return <ClientAppointments />;
      case 'mensajes':
        return <ClientMessages />;
      case 'pagos':
        return <ClientPayments />;
      case 'perfil':
        return <ClientProfile />;
      default:
        return <ClientDashboard onSectionChange={setActiveSection} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
      </div>

      {/* Navigation */}
      <DashboardNav 
        activeSection={activeSection} 
        onSectionChange={setActiveSection} 
      />

      {/* Main Content */}
      <div className="lg:pl-72">
        <div className="relative z-10">
          {renderSection()}
        </div>
      </div>
    </div>
  );
}
