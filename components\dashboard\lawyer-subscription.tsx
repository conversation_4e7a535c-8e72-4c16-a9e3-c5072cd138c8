'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Calendar, 
  DollarSign,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  Star,
  Shield,
  Clock
} from 'lucide-react';
import { SubscriptionService, LawyerSubscription } from '@/services/subscription-service';

export function LawyerSubscription() {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<LawyerSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  const loadSubscription = useCallback(async () => {
    try {
      setLoading(true);
      const userSubscription = await SubscriptionService.getLawyerSubscription(user.id);
      setSubscription(userSubscription);
    } catch (error) {
      console.error('Error loading subscription:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadSubscription();
    }
  }, [user, loadSubscription]);

  const handleCancelSubscription = async () => {
    if (!subscription) return;
    
    const confirmed = confirm('¿Estás seguro de que quieres cancelar tu suscripción? Tu perfil dejará de ser visible para los clientes al final del período actual.');
    
    if (confirmed) {
      try {
        setActionLoading(true);
        await SubscriptionService.cancelSubscription(subscription.id!, true);
        await loadSubscription();
        alert('Suscripción cancelada. Tendrás acceso hasta el final del período actual.');
      } catch (error) {
        console.error('Error cancelling subscription:', error);
        alert('Error al cancelar la suscripción. Intenta de nuevo.');
      } finally {
        setActionLoading(false);
      }
    }
  };

  const handleReactivateSubscription = async () => {
    if (!subscription) return;
    
    try {
      setActionLoading(true);
      await SubscriptionService.reactivateSubscription(subscription.id!);
      await loadSubscription();
      alert('Suscripción reactivada exitosamente.');
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      alert('Error al reactivar la suscripción. Intenta de nuevo.');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusIcon = (status: LawyerSubscription['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'past_due':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'suspended':
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: LawyerSubscription['status']) => {
    switch (status) {
      case 'active':
        return 'from-green-400/20 to-green-500/20 border-green-400/30 text-green-400';
      case 'past_due':
        return 'from-yellow-400/20 to-yellow-500/20 border-yellow-400/30 text-yellow-400';
      case 'suspended':
      case 'cancelled':
        return 'from-red-400/20 to-red-500/20 border-red-400/30 text-red-400';
      default:
        return 'from-gray-400/20 to-gray-500/20 border-gray-400/30 text-gray-400';
    }
  };

  const getStatusText = (status: LawyerSubscription['status']) => {
    switch (status) {
      case 'active':
        return 'Activa';
      case 'past_due':
        return 'Pago Pendiente';
      case 'suspended':
        return 'Suspendida';
      case 'cancelled':
        return 'Cancelada';
      case 'inactive':
        return 'Inactiva';
      default:
        return 'Desconocido';
    }
  };

  if (loading) {
    return (
      <div className="p-4 md:p-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
          <p className="text-gray-400 mt-4 text-lg">Cargando información de suscripción...</p>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="p-4 md:p-8">
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
            <CreditCard className="h-10 w-10 text-gray-500" />
          </div>
          <h3 className="text-xl font-bold text-white mb-2">No hay suscripción activa</h3>
          <p className="text-gray-400 mb-6">
            Necesitas una suscripción activa para que tu perfil sea visible para los clientes.
          </p>
          <Button
            onClick={() => window.location.href = '/es/onboarding/lawyer'}
            className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-6 py-3 rounded-2xl"
          >
            Activar Suscripción
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-8">
      {/* Header */}
      <div className="mb-6 md:mb-8">
        <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent flex items-center">
          <CreditCard className="mr-3 h-6 w-6 md:h-8 md:w-8 text-purple-400" />
          Mi Suscripción
        </h1>
        <p className="mt-2 md:mt-3 text-gray-300 text-base md:text-lg">
          Gestiona tu suscripción y facturación
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
        {/* Subscription Status */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl">
          <CardHeader className="p-6 md:p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Shield className="mr-3 h-6 w-6 text-blue-400" />
              Estado de Suscripción
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 md:p-8">
            <div className="space-y-6">
              {/* Status Badge */}
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Estado:</span>
                <Badge className={`bg-gradient-to-r ${getStatusColor(subscription.status)} flex items-center space-x-2`}>
                  {getStatusIcon(subscription.status)}
                  <span>{getStatusText(subscription.status)}</span>
                </Badge>
              </div>

              {/* Plan Info */}
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Plan:</span>
                <span className="text-white font-semibold">
                  {subscription.plan === 'monthly' ? 'Mensual' : 'Anual'}
                </span>
              </div>

              {/* Amount */}
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Precio:</span>
                <span className="text-yellow-400 font-bold text-lg">
                  ${subscription.amount.toFixed(2)}/{subscription.plan === 'monthly' ? 'mes' : 'año'}
                </span>
              </div>

              {/* Current Period */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Período actual:</span>
                  <span className="text-white text-sm">
                    {new Date(subscription.currentPeriodStart).toLocaleDateString('es-ES')} - {' '}
                    {new Date(subscription.currentPeriodEnd).toLocaleDateString('es-ES')}
                  </span>
                </div>
              </div>

              {/* Next Billing */}
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Próxima facturación:</span>
                <span className="text-white font-semibold">
                  {new Date(subscription.nextBillingDate).toLocaleDateString('es-ES')}
                </span>
              </div>

              {/* Cancel at Period End */}
              {subscription.cancelAtPeriodEnd && (
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-2xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-400" />
                    <span className="text-yellow-400 text-sm font-medium">Cancelación Programada</span>
                  </div>
                  <p className="text-gray-300 text-sm">
                    Tu suscripción se cancelará al final del período actual ({new Date(subscription.currentPeriodEnd).toLocaleDateString('es-ES')}).
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Plan Features */}
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl">
          <CardHeader className="p-6 md:p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl flex items-center">
              <Star className="mr-3 h-6 w-6 text-yellow-400" />
              Beneficios Incluidos
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 md:p-8">
            <div className="space-y-4">
              {SubscriptionService.PLANS.find(p => p.interval === (subscription.plan === 'monthly' ? 'month' : 'year'))?.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl mt-6 md:mt-8">
        <CardHeader className="p-6 md:p-8 border-b border-white/10">
          <CardTitle className="text-white text-xl">Acciones de Suscripción</CardTitle>
        </CardHeader>
        <CardContent className="p-6 md:p-8">
          <div className="flex flex-col md:flex-row gap-4">
            {subscription.status === 'active' && !subscription.cancelAtPeriodEnd && (
              <Button
                onClick={handleCancelSubscription}
                disabled={actionLoading}
                className="bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30 hover:border-red-500/50 rounded-xl px-6 py-3"
              >
                {actionLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Procesando...
                  </>
                ) : (
                  'Cancelar Suscripción'
                )}
              </Button>
            )}

            {subscription.cancelAtPeriodEnd && (
              <Button
                onClick={handleReactivateSubscription}
                disabled={actionLoading}
                className="bg-gradient-to-r from-green-400/20 to-green-500/20 border border-green-400/30 text-green-400 hover:bg-green-400/30 hover:border-green-400/50 rounded-xl px-6 py-3"
              >
                {actionLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Procesando...
                  </>
                ) : (
                  'Reactivar Suscripción'
                )}
              </Button>
            )}

            {(subscription.status === 'past_due' || subscription.status === 'suspended') && (
              <Button
                onClick={() => window.location.href = '/es/onboarding/lawyer'}
                className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-6 py-3 rounded-2xl"
              >
                Actualizar Pago
              </Button>
            )}

            <Button
              onClick={() => window.open('https://billing.stripe.com/p/login/test_your_portal_link', '_blank')}
              className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-6 py-3 rounded-xl"
            >
              <Calendar className="mr-2 h-4 w-4" />
              Portal de Facturación
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
