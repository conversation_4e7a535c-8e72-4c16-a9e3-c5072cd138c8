import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const manifest = {
    name: 'Abogo - Legal Services Puerto Rico',
    short_name: 'Abogo',
    description: 'Find the right legal expert in Puerto Rico',
    start_url: '/es',
    display: 'standalone',
    background_color: '#000000',
    theme_color: '#f59e0b',
    orientation: 'portrait',
    scope: '/',
    lang: 'es',
    icons: [
      {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'maskable'
      },
      {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'maskable'
      }
    ],
    categories: ['business', 'legal', 'professional']
  };

  return NextResponse.json(manifest, {
    headers: {
      'Content-Type': 'application/manifest+json',
    },
  });
}
