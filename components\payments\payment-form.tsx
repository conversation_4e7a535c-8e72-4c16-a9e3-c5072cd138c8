'use client';

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Lock, 
  Shield, 
  CheckCircle,
  AlertCircle,
  Loader2,
  DollarSign,
  Calendar,
  User,
  Clock
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface PaymentFormProps {
  amount: number;
  lawyerId: string;
  lawyerName: string;
  appointmentId?: string;
  appointmentDate?: string;
  appointmentTime?: string;
  specialty?: string;
  description: string;
  onSuccess?: (paymentId: string) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
}

const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      fontSize: '16px',
      color: '#ffffff',
      backgroundColor: 'transparent',
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSmoothing: 'antialiased',
      '::placeholder': {
        color: '#9ca3af',
      },
      iconColor: '#fbbf24',
    },
    invalid: {
      color: '#ef4444',
      iconColor: '#ef4444',
    },
    complete: {
      color: '#10b981',
      iconColor: '#10b981',
    },
  },
  hidePostalCode: false,
  iconStyle: 'solid' as const,
};

function PaymentFormContent({
  amount,
  lawyerId,
  lawyerName,
  appointmentId,
  appointmentDate,
  appointmentTime,
  specialty,
  description,
  onSuccess,
  onError,
  onCancel
}: PaymentFormProps) {
  const { user } = useAuth();
  const stripe = useStripe();
  const elements = useElements();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);

  // Calculate fees
  const platformFee = Math.round(amount * 0.05 * 100) / 100; // 5% platform fee
  const processingFee = Math.round(amount * 0.029 * 100) / 100 + 0.30; // Stripe fee
  const totalAmount = amount;

  useEffect(() => {
    createPaymentIntent();
  }, []);

  const createPaymentIntent = async () => {
    try {
      const response = await fetch('/api/payments/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          clientId: user?.id,
          lawyerId,
          appointmentId,
          description,
          metadata: {
            lawyerName,
            clientName: `${user?.firstName} ${user?.lastName}`,
            appointmentDate,
            appointmentTime,
            specialty
          }
        }),
      });

      const data = await response.json();
      
      if (data.error) {
        setPaymentError(data.error);
        onError?.(data.error);
      } else {
        setClientSecret(data.clientSecret);
        setPaymentId(data.paymentId);
      }
    } catch (error: any) {
      setPaymentError('Error creating payment. Please try again.');
      onError?.(error.message);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setPaymentError('Card element not found');
      setIsProcessing(false);
      return;
    }

    const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: cardElement,
        billing_details: {
          name: `${user?.firstName} ${user?.lastName}`,
          email: user?.email,
        },
      },
    });

    if (error) {
      setPaymentError(error.message || 'Payment failed');
      onError?.(error.message || 'Payment failed');
    } else if (paymentIntent.status === 'succeeded') {
      setPaymentSuccess(true);
      onSuccess?.(paymentId || '');
    }

    setIsProcessing(false);
  };

  if (paymentSuccess) {
    return (
      <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-green-500/20 rounded-3xl max-w-md w-full mx-auto">
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
          <h3 className="text-2xl font-bold text-white mb-4">¡Pago Exitoso!</h3>
          <p className="text-gray-300 mb-6">
            Tu pago de ${amount.toFixed(2)} ha sido procesado exitosamente.
          </p>
          <div className="bg-green-500/10 border border-green-500/20 rounded-2xl p-4 mb-6">
            <p className="text-green-400 text-sm">
              Recibirás un email de confirmación en breve.
            </p>
          </div>
          <Button
            onClick={() => window.location.href = '/es/dashboard/client/appointments'}
            className="w-full bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:via-green-600 hover:to-green-700 text-black font-bold py-3 rounded-2xl"
          >
            Ver Mis Citas
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-md w-full mx-auto space-y-6">
      {/* Payment Summary */}
      <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl">
        <CardHeader className="p-6 border-b border-white/10">
          <CardTitle className="text-white text-xl flex items-center">
            <DollarSign className="mr-3 h-6 w-6 text-yellow-400" />
            Resumen del Pago
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Lawyer Info */}
            <div className="flex items-center space-x-3 p-4 bg-white/5 rounded-2xl">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-xl flex items-center justify-center">
                <User className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <p className="text-white font-semibold">{lawyerName}</p>
                <p className="text-gray-400 text-sm">{specialty || 'Consulta Legal'}</p>
              </div>
            </div>

            {/* Appointment Details */}
            {appointmentDate && appointmentTime && (
              <div className="flex items-center space-x-3 p-4 bg-white/5 rounded-2xl">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-xl flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-green-400" />
                </div>
                <div>
                  <p className="text-white font-semibold">{appointmentDate}</p>
                  <p className="text-gray-400 text-sm flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {appointmentTime}
                  </p>
                </div>
              </div>
            )}

            {/* Payment Breakdown */}
            <div className="space-y-3 pt-4 border-t border-white/10">
              <div className="flex justify-between">
                <span className="text-gray-400">Consulta</span>
                <span className="text-white">${amount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400 text-sm">Tarifa de plataforma (5%)</span>
                <span className="text-gray-400 text-sm">${platformFee.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400 text-sm">Procesamiento</span>
                <span className="text-gray-400 text-sm">Incluido</span>
              </div>
              <div className="flex justify-between pt-3 border-t border-white/10">
                <span className="text-white font-bold">Total</span>
                <span className="text-yellow-400 font-bold text-xl">${totalAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Form */}
      <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-white/10 rounded-3xl">
        <CardHeader className="p-6 border-b border-white/10">
          <CardTitle className="text-white text-xl flex items-center">
            <CreditCard className="mr-3 h-6 w-6 text-blue-400" />
            Información de Pago
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Card Element */}
            <div>
              <label className="block text-white font-medium mb-3">
                Información de la Tarjeta
              </label>
              <div className="p-4 bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl focus-within:border-yellow-500/50 focus-within:ring-2 focus-within:ring-yellow-500/20 transition-all duration-200">
                <CardElement options={CARD_ELEMENT_OPTIONS} />
              </div>
            </div>

            {/* Security Notice */}
            <div className="flex items-center space-x-3 p-4 bg-green-500/10 border border-green-500/20 rounded-2xl">
              <Shield className="h-5 w-5 text-green-400 flex-shrink-0" />
              <div>
                <p className="text-green-400 text-sm font-medium">Pago Seguro</p>
                <p className="text-green-300 text-xs">
                  Protegido por encriptación SSL de 256 bits
                </p>
              </div>
            </div>

            {/* Error Message */}
            {paymentError && (
              <div className="flex items-center space-x-3 p-4 bg-red-500/10 border border-red-500/20 rounded-2xl">
                <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
                <p className="text-red-400 text-sm">{paymentError}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                type="submit"
                disabled={!stripe || isProcessing || !clientSecret}
                className="w-full bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold py-4 rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed text-lg"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Procesando Pago...
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-5 w-5" />
                    Pagar ${totalAmount.toFixed(2)}
                  </>
                )}
              </Button>

              {onCancel && (
                <Button
                  type="button"
                  onClick={onCancel}
                  className="w-full bg-gray-500/20 border border-gray-500/30 text-gray-400 hover:bg-gray-500/30 hover:border-gray-500/50 py-3 rounded-2xl"
                >
                  Cancelar
                </Button>
              )}
            </div>

            {/* Powered by Stripe */}
            <div className="text-center">
              <p className="text-gray-500 text-xs">
                Procesado de forma segura por{' '}
                <span className="text-blue-400 font-medium">Stripe</span>
              </p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export function PaymentForm(props: PaymentFormProps) {
  return (
    <Elements stripe={stripePromise}>
      <PaymentFormContent {...props} />
    </Elements>
  );
}
