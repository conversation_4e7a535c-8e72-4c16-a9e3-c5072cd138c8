'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Star, 
  MapPin,
  Phone,
  Mail,
  Scale,
  Users,
  Briefcase,
  Heart,
  Building,
  Gavel,
  Home,
  Car,
  Shield,
  FileText,
  DollarSign,
  Filter,
  ArrowLeft
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { getFirebaseDb } from '@/lib/firebase';
import Link from 'next/link';

const legalCategories = [
  { id: 'family', name: 'Derecho de Familia', icon: Heart, color: 'from-pink-400 to-rose-500', description: 'Divorcios, custodia, adopción' },
  { id: 'business', name: 'Derecho Corporativo', icon: Building, color: 'from-blue-400 to-blue-600', description: 'Empresas, contratos, fusiones' },
  { id: 'criminal', name: 'Derecho Penal', icon: Gavel, color: 'from-red-400 to-red-600', description: 'Defensa criminal, delitos' },
  { id: 'real-estate', name: 'Derecho Inmobiliario', icon: Home, color: 'from-green-400 to-green-600', description: 'Propiedades, títulos, hipotecas' },
  { id: 'personal-injury', name: 'Lesiones Personales', icon: Shield, color: 'from-orange-400 to-orange-600', description: 'Accidentes, negligencia médica' },
  { id: 'labor', name: 'Derecho Laboral', icon: Users, color: 'from-purple-400 to-purple-600', description: 'Empleo, despidos, discriminación' },
  { id: 'immigration', name: 'Derecho de Inmigración', icon: FileText, color: 'from-indigo-400 to-indigo-600', description: 'Visas, residencia, ciudadanía' },
  { id: 'bankruptcy', name: 'Derecho de Quiebras', icon: DollarSign, color: 'from-yellow-400 to-amber-500', description: 'Bancarrota, deudas, reorganización' },
];

export function LawyersBrowse() {
  const [lawyers, setLawyers] = useState([]);
  const [filteredLawyers, setFilteredLawyers] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadLawyers();
  }, []);

  useEffect(() => {
    filterLawyers();
  }, [lawyers, selectedCategory, searchTerm]);

  const loadLawyers = async () => {
    try {
      setLoading(true);

      // Only load lawyers with active subscriptions and visible profiles
      const lawyersQuery = query(
        collection(getFirebaseDb(), 'lawyers'),
        where('subscriptionStatus', '==', 'active'),
        where('profileVisible', '==', true),
        orderBy('rating', 'desc')
      );

      const lawyersSnapshot = await getDocs(lawyersQuery);
      const lawyersData = lawyersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setLawyers(lawyersData);
    } catch (error) {
      console.error('Error loading lawyers:', error);
      // Create mock data for demonstration
      const mockLawyers = [
        {
          id: '1',
          firstName: 'María',
          lastName: 'González',
          specialty: 'family',
          specialtyName: 'Derecho de Familia',
          rating: 4.9,
          reviews: 127,
          location: 'San Juan, PR',
          description: 'Especialista en derecho de familia con más de 15 años de experiencia. Casos exitosos en divorcios, custodia y adopción.',
          phone: '(*************',
          email: '<EMAIL>',
          price: '$150/hora',
          languages: ['Español', 'Inglés'],
          education: 'JD, Universidad de Puerto Rico',
          experience: '15+ años'
        },
        {
          id: '2',
          firstName: 'Carlos',
          lastName: 'Rodríguez',
          specialty: 'business',
          specialtyName: 'Derecho Corporativo',
          rating: 4.8,
          reviews: 89,
          location: 'Bayamón, PR',
          description: 'Experto en derecho corporativo y transacciones comerciales. Asesoría integral para empresas.',
          phone: '(*************',
          email: '<EMAIL>',
          price: '$200/hora',
          languages: ['Español', 'Inglés'],
          education: 'JD, Harvard Law School',
          experience: '12+ años'
        },
        {
          id: '3',
          firstName: 'Ana',
          lastName: 'Martínez',
          specialty: 'criminal',
          specialtyName: 'Derecho Penal',
          rating: 4.7,
          reviews: 156,
          location: 'Ponce, PR',
          description: 'Defensora criminal experimentada con historial probado en casos complejos.',
          phone: '(*************',
          email: '<EMAIL>',
          price: '$175/hora',
          languages: ['Español', 'Inglés'],
          education: 'JD, Universidad Interamericana',
          experience: '18+ años'
        },
        {
          id: '4',
          firstName: 'Roberto',
          lastName: 'Méndez',
          specialty: 'real-estate',
          specialtyName: 'Derecho Inmobiliario',
          rating: 4.6,
          reviews: 73,
          location: 'Caguas, PR',
          description: 'Especialista en transacciones inmobiliarias y desarrollo de propiedades.',
          phone: '(*************',
          email: '<EMAIL>',
          price: '$125/hora',
          languages: ['Español', 'Inglés'],
          education: 'JD, Universidad de Puerto Rico',
          experience: '10+ años'
        }
      ];
      setLawyers(mockLawyers);
    } finally {
      setLoading(false);
    }
  };

  const filterLawyers = () => {
    let filtered = lawyers;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(lawyer => lawyer.specialty === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(lawyer =>
        lawyer.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lawyer.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lawyer.specialtyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lawyer.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredLawyers(filtered);
  };

  const getCategoryIcon = (categoryId) => {
    const category = legalCategories.find(cat => cat.id === categoryId);
    return category ? category.icon : Scale;
  };

  const getCategoryColor = (categoryId) => {
    const category = legalCategories.find(cat => cat.id === categoryId);
    return category ? category.color : 'from-gray-400 to-gray-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
      </div>

      <div className="relative z-10 p-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/es/dashboard/client">
                <Button className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-4 py-2 rounded-xl">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent">
                  Buscar Abogados
                </h1>
                <p className="mt-3 text-gray-300 text-lg">
                  Encuentra el abogado perfecto para tu caso en Puerto Rico
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Buscar por nombre, especialidad o descripción..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 h-12 bg-black/40 backdrop-blur-xl border border-yellow-500/30 rounded-2xl text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50"
            />
          </div>
        </div>

        {/* Category Filter */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-6">Especialidades Legales</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-6">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`p-4 rounded-2xl border transition-all duration-300 ${
                selectedCategory === 'all'
                  ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border-yellow-400/30 text-yellow-400'
                  : 'bg-black/40 backdrop-blur-xl border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
              }`}
            >
              <Scale className="h-6 w-6 mx-auto mb-2" />
              <span className="text-sm font-medium">Todas</span>
            </button>
            
            {legalCategories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`p-4 rounded-2xl border transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border-yellow-400/30 text-yellow-400'
                      : 'bg-black/40 backdrop-blur-xl border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
                  }`}
                >
                  <Icon className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-xs font-medium text-center">{category.name}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Results */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white">
              {selectedCategory === 'all' 
                ? `${filteredLawyers.length} Abogados Disponibles`
                : `${filteredLawyers.length} Especialistas en ${legalCategories.find(c => c.id === selectedCategory)?.name}`
              }
            </h3>
            <Button className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-4 py-2 rounded-xl">
              <Filter className="mr-2 h-4 w-4" />
              Filtros
            </Button>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
              <p className="text-gray-400 mt-4 text-lg">Cargando abogados...</p>
            </div>
          ) : filteredLawyers.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {filteredLawyers.map((lawyer) => {
                const Icon = getCategoryIcon(lawyer.specialty);
                const colorClass = getCategoryColor(lawyer.specialty);
                
                return (
                  <Card key={lawyer.id} className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 hover:border-yellow-500/30 rounded-3xl overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-yellow-500/10">
                    <CardContent className="p-8">
                      <div className="flex items-start space-x-6">
                        <div className={`w-20 h-20 bg-gradient-to-br ${colorClass} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                          <Icon className="h-10 w-10 text-white" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-2xl font-bold text-white">
                              {lawyer.firstName} {lawyer.lastName}
                            </h3>
                            <div className="flex items-center space-x-1">
                              <Star className="h-5 w-5 text-yellow-400 fill-current" />
                              <span className="text-white font-semibold">{lawyer.rating}</span>
                              <span className="text-gray-400 text-sm">({lawyer.reviews})</span>
                            </div>
                          </div>
                          
                          <Badge className={`mb-3 bg-gradient-to-r ${colorClass} text-white border-0`}>
                            {lawyer.specialtyName}
                          </Badge>
                          
                          <p className="text-gray-300 mb-4 line-clamp-2">
                            {lawyer.description}
                          </p>
                          
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-400 mb-6">
                            <div className="flex items-center">
                              <MapPin className="h-4 w-4 mr-2" />
                              {lawyer.location}
                            </div>
                            <div className="flex items-center">
                              <DollarSign className="h-4 w-4 mr-2" />
                              {lawyer.price}
                            </div>
                            <div className="flex items-center">
                              <Briefcase className="h-4 w-4 mr-2" />
                              {lawyer.experience}
                            </div>
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-2" />
                              {lawyer.languages?.join(', ')}
                            </div>
                          </div>
                          
                          <div className="flex space-x-3">
                            <Link href={`/es/lawyers/${lawyer.id}`} className="flex-1">
                              <Button className="w-full bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold py-3 rounded-2xl">
                                Ver Perfil
                              </Button>
                            </Link>
                            <Button className="bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 hover:border-green-500/50 px-6 py-3 rounded-2xl">
                              <Mail className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <Search className="h-10 w-10 text-gray-500" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">No se encontraron abogados</h3>
              <p className="text-gray-400 mb-6">
                Intenta con otros términos de búsqueda o selecciona una categoría diferente
              </p>
              <Button 
                onClick={() => {
                  setSelectedCategory('all');
                  setSearchTerm('');
                }}
                className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-8 py-3 rounded-2xl"
              >
                Ver Todos los Abogados
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
