'use client';

import * as React from 'react';
import { useAuth } from '@/contexts/auth-context';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { LawyerDashboard } from '@/components/dashboard/lawyer-dashboard';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function LawyerDashboardPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && user) {
      // Redirect clients to their dashboard
      if (user.role === 'client') {
        router.push('/dashboard/client');
        return;
      }
      // Redirect admins to their dashboard (we'll keep admin in the original dashboard)
      if (user.role === 'admin') {
        router.push('/dashboard');
        return;
      }
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!user) {
    // Redirect to login will be handled by middleware
    return null;
  }

  // Only show lawyer dashboard for lawyers
  if (user.role !== 'lawyer') {
    return null;
  }

  return (
    <DashboardLayout>
      <LawyerDashboard />
    </DashboardLayout>
  );
}
