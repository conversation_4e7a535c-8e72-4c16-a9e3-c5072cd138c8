'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Users,
  Scale,
  Clock,
  Star,
  BarChart3,
  Award,
  Target,
  Zap,
  Shield
} from 'lucide-react';

const platformStats = [
  {
    title: 'Casos Resueltos',
    value: '15,247',
    change: '+12.5%',
    trend: 'up',
    icon: Scale,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  {
    title: 'Abogados Activos',
    value: '1,234',
    change: '+8.2%',
    trend: 'up',
    icon: Users,
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  {
    title: 'Satisfacción Cliente',
    value: '98.7%',
    change: '+2.1%',
    trend: 'up',
    icon: Star,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50'
  },
  {
    title: 'Tiempo Promedio',
    value: '4.2 días',
    change: '-15.3%',
    trend: 'down',
    icon: Clock,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  }
];



const achievements = [
  {
    title: 'Plataforma Legal #1',
    description: 'Reconocida como la mejor plataforma legal de Puerto Rico',
    icon: Award,
    year: '2023'
  },
  {
    title: 'Certificación ISO 27001',
    description: 'Máxima seguridad en protección de datos',
    icon: Shield,
    year: '2023'
  },
  {
    title: 'Premio Innovación Digital',
    description: 'Por transformar el acceso a servicios legales',
    icon: Zap,
    year: '2022'
  },
  {
    title: 'Mejor Startup Legal',
    description: 'Reconocimiento del Colegio de Abogados de PR',
    icon: Target,
    year: '2022'
  }
];

export default function AnalyticsShowcase() {

  return (
    <section id="impact" className="relative py-24 overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <Badge className="mb-4 bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30 border border-yellow-500/30 text-sm font-medium">
              <BarChart3 className="w-4 h-4 mr-2" />
              Impacto Medible
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Nuestro Impacto en la Justicia de Puerto Rico</h2>
            <p className="text-lg text-gray-300 max-w-3xl mx-auto">
              Datos en tiempo real que demuestran cómo estamos transformando el acceso a la justicia en la isla.
            </p>
          </div>

          {/* Main Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {platformStats.map((stat, index) => (
              <Card key={index} className="text-center p-6 bg-white/5 backdrop-blur-sm border border-white/10 hover:border-yellow-500/30 transition-colors">
                <CardContent className="p-0">
                  <div className="w-16 h-16 bg-yellow-500/10 rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-500/20">
                    <stat.icon className="h-6 w-6 text-yellow-400" />
                  </div>
                  <div className="text-3xl font-bold text-yellow-400 mb-2">{stat.value}</div>
                  <div className="text-sm text-gray-300 mb-2">{stat.title}</div>
                  {stat.change && (
                    <div className={`text-xs font-medium ${stat.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                      {stat.change}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Success Metrics */}
        <Card className="mb-16 bg-white/5 backdrop-blur-sm border border-white/10 hover:border-yellow-500/30 transition-colors">
          <CardContent className="p-8">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold mb-2 text-yellow-400">$127M+</div>
                <div className="text-gray-300">Recuperado para clientes</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2 text-yellow-400">24/7</div>
                <div className="text-gray-300">Soporte disponible</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2 text-yellow-400">99.9%</div>
                <div className="text-gray-300">Tiempo de actividad</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Achievements */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-white mb-4">
            Reconocimientos y Certificaciones
          </h3>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Nuestro compromiso con la excelencia ha sido reconocido por las principales organizaciones del sector legal
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {achievements.map((achievement, index) => (
            <Card key={index} className="text-center p-6 bg-white/5 backdrop-blur-sm border border-white/10 hover:border-yellow-500/30 transition-colors">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-yellow-500/10 rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-500/20">
                  <achievement.icon className="h-8 w-8 text-yellow-400" />
                </div>
                <Badge className="mb-3 bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">{achievement.year}</Badge>
                <h4 className="text-lg font-semibold text-white mb-2">{achievement.title}</h4>
                <p className="text-gray-300 text-sm leading-relaxed">{achievement.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <Card className="bg-white/5 backdrop-blur-sm border border-white/10 hover:border-yellow-500/30 transition-colors">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-white mb-4">
                ¿Listo para formar parte de estos números?
              </h3>
              <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
                Únete a miles de clientes satisfechos y abogados exitosos en la plataforma legal más confiable de Puerto Rico.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="px-8 py-3 border border-transparent text-base font-medium rounded-lg text-gray-900 bg-gradient-to-r from-yellow-500 via-yellow-400 to-yellow-600 hover:from-yellow-400 hover:via-yellow-300 hover:to-yellow-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-yellow-500/30">
                  Buscar Abogado
                </Button>
                <Button variant="outline" className="px-8 py-3 border-2 border-yellow-400 text-base font-medium rounded-lg text-yellow-400 bg-transparent hover:bg-yellow-500/10 transition-all duration-200 transform hover:scale-105 hover:border-yellow-300">
                  Registrarse como Abogado
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
