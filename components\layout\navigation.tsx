'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Scale, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const t = useTranslations('navigation');

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <nav className="bg-gradient-to-r from-gray-900 via-gray-900 to-black fixed w-full z-50 backdrop-blur-md bg-opacity-95 border-b border-yellow-600/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-20">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <Scale className="h-10 w-10 text-yellow-500" />
                <span className="ml-3 text-2xl font-bold text-white">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-400">
                    Abogo
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className="bg-gradient-to-r from-gray-900 via-gray-900 to-black fixed w-full z-50 backdrop-blur-md bg-opacity-95 border-b border-yellow-600/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-20">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <Scale className="h-10 w-10 text-yellow-500" />
              <span className="ml-3 text-2xl font-bold text-white">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-400">
                  Abogo
                </span>
              </span>
            </div>
            <div className="hidden md:ml-12 md:flex md:space-x-8">
              <Link 
                href="#hero" 
                className="relative group text-white px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                Inicio
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
              <Link 
                href="#services-showcase" 
                className="relative group text-white/90 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                Servicios
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
              <Link 
                href="#lawyers" 
                className="relative group text-white/90 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                Nuestros Abogados
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
              <Link 
                href="#impact" 
                className="relative group text-white/90 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                Nuestro Impacto
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/auth/signin" passHref>
              <Button 
                variant="ghost" 
                className="hidden md:inline-flex items-center px-6 py-2 text-sm font-medium rounded-lg text-amber-400 hover:text-amber-200 hover:bg-amber-900/30 transition-all duration-200 border border-amber-600/40 hover:border-amber-500/60"
              >
                Iniciar Sesión
              </Button>
            </Link>
            <Link href="/auth/signup" passHref>
              <Button className="hidden md:inline-flex items-center px-6 py-2.5 text-sm font-medium rounded-lg bg-gradient-to-r from-amber-500 via-amber-400 to-amber-600 text-black hover:from-amber-400 hover:via-amber-300 hover:to-amber-500 transition-all duration-200 shadow-lg shadow-amber-500/20 hover:shadow-amber-400/30">
                Regístrate Gratis
              </Button>
            </Link>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Button
                  variant="ghost"
                  className="md:hidden p-2 rounded-lg text-white hover:bg-white/20 focus:outline-none transition-colors"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  <span className="sr-only">Open main menu</span>
                  {isMobileMenuOpen ? (
                    <X className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Menu className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`md:hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="pt-2 pb-4 space-y-1 bg-gradient-to-b from-gray-900 to-black shadow-xl border-t border-yellow-600/30">
          <Link
            href="#hero"
            className="block px-6 py-3 text-base font-medium text-white hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Inicio
          </Link>
          <Link
            href="#services-showcase"
            className="block px-6 py-3 text-base font-medium text-white hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Servicios
          </Link>
          <Link
            href="#how-it-works"
            className="block px-6 py-3 text-base font-medium text-white hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Cómo Funciona
          </Link>
          <Link
            href="#impact"
            className="block px-6 py-3 text-base font-medium text-white hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Nuestro Impacto
          </Link>
          <div className="pt-4 pb-3 border-t border-white/10 px-4 mt-2">
            <div className="space-y-3">
              <Link 
                href="/auth/signin"
                className="w-full"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Button 
                  variant="ghost" 
                  className="w-full justify-start px-6 py-3 text-base font-medium text-amber-400 hover:bg-amber-500/10"
                >
                  Iniciar Sesión
                </Button>
              </Link>
              <Link 
                href="/auth/signup"
                className="w-full"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Button 
                  className="w-full justify-center px-6 py-3 text-base font-medium bg-gradient-to-r from-amber-500 to-amber-600 text-gray-900 hover:from-amber-400 hover:to-amber-500"
                >
                  Regístrate Gratis
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
