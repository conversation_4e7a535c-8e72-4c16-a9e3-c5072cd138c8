export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  actionUrl?: string;
  actionText?: string;
}

export type NotificationType = 
  // Appointment notifications
  | 'appointment_request'
  | 'appointment_confirmed'
  | 'appointment_cancelled'
  | 'appointment_reminder'
  | 'appointment_completed'
  | 'appointment_rescheduled'
  
  // Message notifications
  | 'new_message'
  | 'message_reply'
  
  // Payment notifications
  | 'payment_received'
  | 'payment_pending'
  | 'payment_failed'
  | 'invoice_generated'
  
  // Review notifications
  | 'new_review'
  | 'review_response'
  
  // Profile notifications
  | 'profile_incomplete'
  | 'profile_verified'
  | 'profile_updated'
  
  // System notifications
  | 'system_maintenance'
  | 'system_update'
  | 'account_security'
  | 'welcome'
  | 'subscription_expiring'
  | 'subscription_expired'
  
  // Lawyer-specific notifications
  | 'new_client_inquiry'
  | 'client_booking_request'
  | 'availability_reminder'
  | 'earnings_summary'
  
  // Client-specific notifications
  | 'lawyer_response'
  | 'consultation_available'
  | 'document_ready';

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
}

export interface UserNotificationSettings {
  userId: string;
  preferences: {
    [key in NotificationType]?: NotificationPreferences;
  };
  globalSettings: {
    email: boolean;
    push: boolean;
    sms: boolean;
    inApp: boolean;
    quietHours: {
      enabled: boolean;
      start: string; // HH:mm format
      end: string;   // HH:mm format
    };
  };
  updatedAt: string;
}

export interface NotificationTemplate {
  type: NotificationType;
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionText?: string;
  variables: string[]; // Variables that can be replaced in title/message
}

export const NOTIFICATION_TEMPLATES: Record<NotificationType, NotificationTemplate> = {
  // Appointment notifications
  appointment_request: {
    type: 'appointment_request',
    title: 'Nueva solicitud de cita',
    message: '{clientName} ha solicitado una cita para {specialty} el {date} a las {time}',
    priority: 'high',
    actionText: 'Ver solicitud',
    variables: ['clientName', 'specialty', 'date', 'time']
  },
  appointment_confirmed: {
    type: 'appointment_confirmed',
    title: 'Cita confirmada',
    message: 'Tu cita con {lawyerName} ha sido confirmada para el {date} a las {time}',
    priority: 'high',
    actionText: 'Ver detalles',
    variables: ['lawyerName', 'date', 'time']
  },
  appointment_cancelled: {
    type: 'appointment_cancelled',
    title: 'Cita cancelada',
    message: 'Tu cita con {lawyerName} programada para el {date} ha sido cancelada',
    priority: 'high',
    actionText: 'Reagendar',
    variables: ['lawyerName', 'date', 'reason']
  },
  appointment_reminder: {
    type: 'appointment_reminder',
    title: 'Recordatorio de cita',
    message: 'Tienes una cita con {lawyerName} mañana a las {time}',
    priority: 'medium',
    actionText: 'Ver detalles',
    variables: ['lawyerName', 'time', 'location']
  },
  appointment_completed: {
    type: 'appointment_completed',
    title: 'Cita completada',
    message: 'Tu consulta con {lawyerName} ha sido completada. ¿Te gustaría dejar una reseña?',
    priority: 'medium',
    actionText: 'Dejar reseña',
    variables: ['lawyerName']
  },
  appointment_rescheduled: {
    type: 'appointment_rescheduled',
    title: 'Cita reprogramada',
    message: 'Tu cita con {lawyerName} ha sido reprogramada para el {newDate} a las {newTime}',
    priority: 'high',
    actionText: 'Ver detalles',
    variables: ['lawyerName', 'newDate', 'newTime', 'oldDate', 'oldTime']
  },

  // Message notifications
  new_message: {
    type: 'new_message',
    title: 'Nuevo mensaje',
    message: '{senderName} te ha enviado un mensaje',
    priority: 'medium',
    actionText: 'Leer mensaje',
    variables: ['senderName', 'preview']
  },
  message_reply: {
    type: 'message_reply',
    title: 'Respuesta recibida',
    message: '{senderName} ha respondido a tu mensaje',
    priority: 'medium',
    actionText: 'Ver conversación',
    variables: ['senderName', 'preview']
  },

  // Payment notifications
  payment_received: {
    type: 'payment_received',
    title: 'Pago recibido',
    message: 'Has recibido un pago de ${amount} de {clientName}',
    priority: 'medium',
    actionText: 'Ver detalles',
    variables: ['amount', 'clientName', 'service']
  },
  payment_pending: {
    type: 'payment_pending',
    title: 'Pago pendiente',
    message: 'Tienes un pago pendiente de ${amount} para {service}',
    priority: 'medium',
    actionText: 'Pagar ahora',
    variables: ['amount', 'service', 'dueDate']
  },
  payment_failed: {
    type: 'payment_failed',
    title: 'Pago fallido',
    message: 'No se pudo procesar tu pago de ${amount}. Por favor verifica tu método de pago',
    priority: 'high',
    actionText: 'Actualizar pago',
    variables: ['amount', 'reason']
  },
  invoice_generated: {
    type: 'invoice_generated',
    title: 'Nueva factura',
    message: 'Se ha generado una nueva factura por ${amount}',
    priority: 'medium',
    actionText: 'Ver factura',
    variables: ['amount', 'invoiceNumber', 'dueDate']
  },

  // Review notifications
  new_review: {
    type: 'new_review',
    title: 'Nueva reseña',
    message: '{clientName} ha dejado una reseña de {rating} estrellas',
    priority: 'medium',
    actionText: 'Ver reseña',
    variables: ['clientName', 'rating', 'comment']
  },
  review_response: {
    type: 'review_response',
    title: 'Respuesta a reseña',
    message: '{lawyerName} ha respondido a tu reseña',
    priority: 'low',
    actionText: 'Ver respuesta',
    variables: ['lawyerName']
  },

  // Profile notifications
  profile_incomplete: {
    type: 'profile_incomplete',
    title: 'Completa tu perfil',
    message: 'Completa tu perfil para aumentar tu visibilidad y obtener más clientes',
    priority: 'medium',
    actionText: 'Completar perfil',
    variables: ['completionPercentage']
  },
  profile_verified: {
    type: 'profile_verified',
    title: 'Perfil verificado',
    message: '¡Felicidades! Tu perfil ha sido verificado exitosamente',
    priority: 'medium',
    actionText: 'Ver perfil',
    variables: []
  },
  profile_updated: {
    type: 'profile_updated',
    title: 'Perfil actualizado',
    message: 'Tu perfil ha sido actualizado exitosamente',
    priority: 'low',
    variables: []
  },

  // System notifications
  system_maintenance: {
    type: 'system_maintenance',
    title: 'Mantenimiento programado',
    message: 'La plataforma estará en mantenimiento el {date} de {startTime} a {endTime}',
    priority: 'medium',
    variables: ['date', 'startTime', 'endTime']
  },
  system_update: {
    type: 'system_update',
    title: 'Nueva actualización',
    message: 'Hemos lanzado nuevas funcionalidades. ¡Descúbrelas!',
    priority: 'low',
    actionText: 'Ver novedades',
    variables: ['features']
  },
  account_security: {
    type: 'account_security',
    title: 'Alerta de seguridad',
    message: 'Se ha detectado un inicio de sesión desde un nuevo dispositivo',
    priority: 'urgent',
    actionText: 'Revisar actividad',
    variables: ['device', 'location', 'time']
  },
  welcome: {
    type: 'welcome',
    title: '¡Bienvenido a Abogo!',
    message: 'Gracias por unirte a nuestra plataforma. Comienza explorando nuestros servicios',
    priority: 'medium',
    actionText: 'Comenzar',
    variables: ['firstName']
  },
  subscription_expiring: {
    type: 'subscription_expiring',
    title: 'Suscripción por vencer',
    message: 'Tu suscripción vence en {days} días. Renueva para continuar disfrutando de todos los beneficios',
    priority: 'medium',
    actionText: 'Renovar',
    variables: ['days', 'planName']
  },
  subscription_expired: {
    type: 'subscription_expired',
    title: 'Suscripción vencida',
    message: 'Tu suscripción ha vencido. Renueva para continuar accediendo a todas las funcionalidades',
    priority: 'high',
    actionText: 'Renovar ahora',
    variables: ['planName']
  },

  // Lawyer-specific notifications
  new_client_inquiry: {
    type: 'new_client_inquiry',
    title: 'Nueva consulta de cliente',
    message: '{clientName} está interesado en tus servicios de {specialty}',
    priority: 'high',
    actionText: 'Ver consulta',
    variables: ['clientName', 'specialty', 'message']
  },
  client_booking_request: {
    type: 'client_booking_request',
    title: 'Solicitud de reserva',
    message: '{clientName} quiere agendar una cita contigo',
    priority: 'high',
    actionText: 'Ver solicitud',
    variables: ['clientName', 'preferredDate', 'preferredTime']
  },
  availability_reminder: {
    type: 'availability_reminder',
    title: 'Actualiza tu disponibilidad',
    message: 'No has actualizado tu disponibilidad en {days} días',
    priority: 'medium',
    actionText: 'Actualizar horarios',
    variables: ['days']
  },
  earnings_summary: {
    type: 'earnings_summary',
    title: 'Resumen de ganancias',
    message: 'Has ganado ${amount} este mes con {appointments} citas completadas',
    priority: 'low',
    actionText: 'Ver reporte',
    variables: ['amount', 'appointments', 'period']
  },

  // Client-specific notifications
  lawyer_response: {
    type: 'lawyer_response',
    title: 'Respuesta de abogado',
    message: '{lawyerName} ha respondido a tu consulta',
    priority: 'high',
    actionText: 'Ver respuesta',
    variables: ['lawyerName']
  },
  consultation_available: {
    type: 'consultation_available',
    title: 'Consulta disponible',
    message: '{lawyerName} tiene disponibilidad para una consulta',
    priority: 'medium',
    actionText: 'Agendar cita',
    variables: ['lawyerName', 'availableDates']
  },
  document_ready: {
    type: 'document_ready',
    title: 'Documento listo',
    message: 'Tu documento {documentType} está listo para descarga',
    priority: 'medium',
    actionText: 'Descargar',
    variables: ['documentType', 'lawyerName']
  }
};
