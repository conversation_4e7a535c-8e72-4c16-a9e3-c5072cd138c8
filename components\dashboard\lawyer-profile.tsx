'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ProfileImageUpload } from '@/components/profile/profile-image-upload';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Save,
  Edit,
  Briefcase,
  GraduationCap,
  Award,
  Plus,
  X,
  Star,
  DollarSign
} from 'lucide-react';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

const legalSpecialties = [
  'Derecho de Familia',
  'Derecho Corporativo',
  'Derecho Penal',
  'Derecho Inmobiliario',
  'Lesiones Personales',
  'Derecho Laboral',
  'Derecho de Inmigración',
  'Derecho de Quiebras',
  'Derecho Tributario',
  'Derecho de Propiedad Intelectual'
];

export function LawyerProfile() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileImage, setProfileImage] = useState(user?.profileImage || '');
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    location: '',
    specialty: '',
    specialtyName: '',
    description: '',
    price: '',
    priceType: 'hora',
    languages: ['Español'],
    education: [''],
    experience: '',
    certifications: [''],
    services: ['']
  });

  const loadLawyerProfile = useCallback(async () => {
    try {
      const lawyerDoc = await getDoc(doc(db, 'lawyers', user.id));
      if (lawyerDoc.exists()) {
        const lawyerData = lawyerDoc.data();
        setFormData({
          firstName: lawyerData.firstName || user.firstName || '',
          lastName: lawyerData.lastName || user.lastName || '',
          email: lawyerData.email || user.email || '',
          phone: lawyerData.phone || user.phone || '',
          location: lawyerData.location || '',
          specialty: lawyerData.specialty || '',
          specialtyName: lawyerData.specialtyName || '',
          description: lawyerData.description || '',
          price: lawyerData.price || '',
          priceType: lawyerData.priceType || 'hora',
          languages: lawyerData.languages || ['Español'],
          education: lawyerData.education || [''],
          experience: lawyerData.experience || '',
          certifications: lawyerData.certifications || [''],
          services: lawyerData.services || ['']
        });
        setProfileImage(lawyerData.profileImage || '');
      }
    } catch (error) {
      console.error('Error loading lawyer profile:', error);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadLawyerProfile();
    }
  }, [user, loadLawyerProfile]);

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleImageUpdate = (newImageUrl) => {
    setProfileImage(newImageUrl);
  };

  const saveProfile = async () => {
    try {
      setLoading(true);

      const updateData = {
        ...formData,
        profileImage,
        updatedAt: new Date().toISOString()
      };

      // Update lawyers collection
      await updateDoc(doc(db, 'lawyers', user.id), updateData);

      // Update users collection
      await updateDoc(doc(db, 'users', user.id), {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        profileImage,
        updatedAt: new Date().toISOString()
      });

      setIsEditing(false);
      alert('Perfil actualizado exitosamente');
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error al guardar el perfil');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent flex items-center">
              <User className="mr-3 h-8 w-8 text-purple-400" />
              Mi Perfil Profesional
            </h1>
            <p className="mt-3 text-gray-300 text-lg">
              Gestiona tu información profesional y especialidades
            </p>
          </div>
          <Button
            onClick={() => setIsEditing(!isEditing)}
            className="bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/30 hover:border-yellow-400/50 rounded-xl px-6 py-3"
          >
            <Edit className="mr-2 h-4 w-4" />
            {isEditing ? 'Cancelar' : 'Editar Perfil'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Profile Image Section */}
        <div className="lg:col-span-1">
          <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl mb-6">
            <CardHeader className="p-6 text-center">
              <CardTitle className="text-white text-lg">Foto de Perfil</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ProfileImageUpload
                currentImageUrl={profileImage}
                onImageUpdate={handleImageUpdate}
                size="xl"
                className="w-full"
              />
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Calificación</span>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                    <span className="text-white font-semibold">4.8</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Reseñas</span>
                  <span className="text-white font-semibold">127</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Clientes</span>
                  <span className="text-white font-semibold">89</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Experiencia</span>
                  <span className="text-white font-semibold">{formData.experience || 'No especificado'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Profile Form */}
        <div className="lg:col-span-2">
          <div className="space-y-8">
            {/* Basic Information */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
              <CardHeader className="p-8 border-b border-white/10">
                <CardTitle className="text-white text-xl">Información Básica</CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="firstName" className="text-white font-medium mb-2 block">
                      Nombre
                    </Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleChange('firstName', e.target.value)}
                      disabled={!isEditing}
                      className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl h-12 disabled:opacity-60"
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName" className="text-white font-medium mb-2 block">
                      Apellido
                    </Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleChange('lastName', e.target.value)}
                      disabled={!isEditing}
                      className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl h-12 disabled:opacity-60"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-white font-medium mb-2 block flex items-center">
                      <Mail className="mr-2 h-4 w-4" />
                      Correo Electrónico
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleChange('email', e.target.value)}
                      disabled={!isEditing}
                      className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl h-12 disabled:opacity-60"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-white font-medium mb-2 block flex items-center">
                      <Phone className="mr-2 h-4 w-4" />
                      Teléfono
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleChange('phone', e.target.value)}
                      disabled={!isEditing}
                      className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl h-12 disabled:opacity-60"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="location" className="text-white font-medium mb-2 block flex items-center">
                      <MapPin className="mr-2 h-4 w-4" />
                      Ubicación
                    </Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => handleChange('location', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Ciudad, Puerto Rico"
                      className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl h-12 disabled:opacity-60"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Professional Information */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
              <CardHeader className="p-8 border-b border-white/10">
                <CardTitle className="text-white text-xl flex items-center">
                  <Briefcase className="mr-3 h-6 w-6" />
                  Información Profesional
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="specialty" className="text-white font-medium mb-2 block">
                        Especialidad Principal
                      </Label>
                      <select
                        id="specialty"
                        value={formData.specialtyName}
                        onChange={(e) => {
                          handleChange('specialtyName', e.target.value);
                          handleChange('specialty', e.target.value.toLowerCase().replace(/\s+/g, '-'));
                        }}
                        disabled={!isEditing}
                        className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 disabled:opacity-60 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                      >
                        <option value="">Seleccionar especialidad</option>
                        {legalSpecialties.map(specialty => (
                          <option key={specialty} value={specialty} className="bg-black text-white">
                            {specialty}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="experience" className="text-white font-medium mb-2 block">
                        Años de Experiencia
                      </Label>
                      <Input
                        id="experience"
                        value={formData.experience}
                        onChange={(e) => handleChange('experience', e.target.value)}
                        disabled={!isEditing}
                        placeholder="ej. 15+ años"
                        className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl h-12 disabled:opacity-60"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="price" className="text-white font-medium mb-2 block flex items-center">
                        <DollarSign className="mr-2 h-4 w-4" />
                        Tarifa
                      </Label>
                      <Input
                        id="price"
                        value={formData.price}
                        onChange={(e) => handleChange('price', e.target.value)}
                        disabled={!isEditing}
                        placeholder="150"
                        className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl h-12 disabled:opacity-60"
                      />
                    </div>
                    <div>
                      <Label htmlFor="priceType" className="text-white font-medium mb-2 block">
                        Tipo de Tarifa
                      </Label>
                      <select
                        id="priceType"
                        value={formData.priceType}
                        onChange={(e) => handleChange('priceType', e.target.value)}
                        disabled={!isEditing}
                        className="w-full bg-black/40 backdrop-blur-xl border border-white/20 text-white rounded-xl h-12 px-3 disabled:opacity-60 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
                      >
                        <option value="hora" className="bg-black text-white">Por hora</option>
                        <option value="consulta" className="bg-black text-white">Por consulta</option>
                        <option value="caso" className="bg-black text-white">Por caso</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-white font-medium mb-2 block">
                      Descripción Profesional
                    </Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleChange('description', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Describe tu experiencia, especialidades y enfoque profesional..."
                      rows={4}
                      className="bg-black/40 backdrop-blur-xl border border-white/20 text-white placeholder:text-gray-400 rounded-xl disabled:opacity-60 resize-none"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Save Button */}
            {isEditing && (
              <div className="flex justify-end">
                <Button
                  onClick={saveProfile}
                  disabled={loading}
                  className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-8 py-3 rounded-2xl disabled:opacity-50"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                      Guardando...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Guardar Cambios
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
