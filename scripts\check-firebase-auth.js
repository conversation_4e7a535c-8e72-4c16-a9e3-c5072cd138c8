#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('🔥 Checking Firebase Authentication Status\n');

try {
  console.log('📊 Getting Firebase project info...');
  
  // Check if authentication is configured
  console.log('\n🔍 Checking Authentication configuration...');
  
  // Try to get auth config (this will fail if auth is not enabled)
  try {
    const authResult = execSync('npx firebase auth:export users.json --project delawpr', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    console.log('✅ Authentication is enabled and configured');
    
    // Clean up the export file
    const fs = require('fs');
    if (fs.existsSync('users.json')) {
      fs.unlinkSync('users.json');
    }
    
  } catch (authError) {
    if (authError.message.includes('not been initialized')) {
      console.log('❌ Authentication is NOT enabled');
      console.log('\n📝 To enable Authentication:');
      console.log('1. Go to: https://console.firebase.google.com/project/delawpr/authentication');
      console.log('2. Click "Get started"');
      console.log('3. Enable Email/Password sign-in method');
      console.log('4. See ENABLE_FIREBASE_AUTH.md for detailed instructions');
    } else {
      console.log('⚠️  Authentication status unclear:', authError.message);
    }
  }
  
  console.log('\n🔗 Firebase Console Links:');
  console.log('- Project: https://console.firebase.google.com/project/delawpr');
  console.log('- Authentication: https://console.firebase.google.com/project/delawpr/authentication');
  console.log('- Firestore: https://console.firebase.google.com/project/delawpr/firestore');
  
} catch (error) {
  console.error('❌ Error checking Firebase status:', error.message);
  console.log('\n📖 Please check ENABLE_FIREBASE_AUTH.md for manual setup instructions');
}
