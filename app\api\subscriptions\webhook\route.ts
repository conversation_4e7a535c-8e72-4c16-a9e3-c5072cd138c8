import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { SubscriptionService } from '@/services/subscription-service';
import { headers } from 'next/headers';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { getFirebaseDb } from '@/lib/firebase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = headers().get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe signature' },
        { status: 400 }
      );
    }

    // Verify webhook signature
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );

    // Handle the event
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;
      
      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(event.data.object);
        break;
      
      default:
        console.log(`Unhandled subscription event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error: any) {
    console.error('Subscription webhook error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 400 }
    );
  }
}

async function handleSubscriptionCreated(subscription: any) {
  try {
    console.log('Subscription created:', subscription.id);
    
    // Find subscription in Firestore by Stripe subscription ID
    const subscriptionRecord = await findSubscriptionByStripeId(subscription.id);
    
    if (subscriptionRecord) {
      await SubscriptionService.updateSubscriptionStatus(
        subscriptionRecord.id!,
        'active',
        {
          stripeSubscriptionId: subscription.id,
          currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
          nextBillingDate: new Date(subscription.current_period_end * 1000).toISOString()
        }
      );
    }
  } catch (error) {
    console.error('Error handling subscription created:', error);
  }
}

async function handleSubscriptionUpdated(subscription: any) {
  try {
    console.log('Subscription updated:', subscription.id);
    
    const subscriptionRecord = await findSubscriptionByStripeId(subscription.id);
    
    if (subscriptionRecord) {
      let status: any = 'active';
      
      // Map Stripe status to our status
      switch (subscription.status) {
        case 'active':
          status = 'active';
          break;
        case 'past_due':
          status = 'past_due';
          break;
        case 'canceled':
          status = 'cancelled';
          break;
        case 'unpaid':
          status = 'suspended';
          break;
        default:
          status = subscription.status;
      }

      await SubscriptionService.updateSubscriptionStatus(
        subscriptionRecord.id!,
        status,
        {
          currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
          nextBillingDate: new Date(subscription.current_period_end * 1000).toISOString(),
          cancelAtPeriodEnd: subscription.cancel_at_period_end
        }
      );
    }
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionDeleted(subscription: any) {
  try {
    console.log('Subscription deleted:', subscription.id);
    
    const subscriptionRecord = await findSubscriptionByStripeId(subscription.id);
    
    if (subscriptionRecord) {
      await SubscriptionService.updateSubscriptionStatus(
        subscriptionRecord.id!,
        'cancelled'
      );
    }
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice: any) {
  try {
    console.log('Invoice payment succeeded:', invoice.id);
    
    if (invoice.subscription) {
      const subscriptionRecord = await findSubscriptionByStripeId(invoice.subscription);
      
      if (subscriptionRecord) {
        // Update subscription to active if it was past due
        if (subscriptionRecord.status === 'past_due' || subscriptionRecord.status === 'inactive') {
          await SubscriptionService.updateSubscriptionStatus(
            subscriptionRecord.id!,
            'active'
          );
        }
      }
    }
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleInvoicePaymentFailed(invoice: any) {
  try {
    console.log('Invoice payment failed:', invoice.id);
    
    if (invoice.subscription) {
      const subscriptionRecord = await findSubscriptionByStripeId(invoice.subscription);
      
      if (subscriptionRecord) {
        await SubscriptionService.updateSubscriptionStatus(
          subscriptionRecord.id!,
          'past_due'
        );
      }
    }
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}

async function handleTrialWillEnd(subscription: any) {
  try {
    console.log('Trial will end:', subscription.id);
    
    const subscriptionRecord = await findSubscriptionByStripeId(subscription.id);
    
    if (subscriptionRecord) {
      // Send notification about trial ending
      // This would be implemented in the notification service
      console.log('Trial ending notification should be sent');
    }
  } catch (error) {
    console.error('Error handling trial will end:', error);
  }
}

async function findSubscriptionByStripeId(stripeSubscriptionId: string) {
  try {
    const subscriptionsQuery = query(
      collection(getFirebaseDb(), 'lawyerSubscriptions'),
      where('stripeSubscriptionId', '==', stripeSubscriptionId)
    );

    const subscriptionsSnapshot = await getDocs(subscriptionsQuery);
    
    if (!subscriptionsSnapshot.empty) {
      const doc = subscriptionsSnapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    }
    
    return null;
  } catch (error) {
    console.error('Error finding subscription by Stripe ID:', error);
    return null;
  }
}
