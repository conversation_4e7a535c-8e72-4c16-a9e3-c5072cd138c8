'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Star,
  MapPin,
  Phone,
  Mail,
  Calendar,
  MessageSquare,
  Briefcase,
  GraduationCap,
  Award,
  Clock,
  DollarSign,
  Users,
  Shield,
  CheckCircle,
  Heart,
  Share
} from 'lucide-react';
import { PaymentModal } from '@/components/payments/payment-modal';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/auth-context';
import Link from 'next/link';

interface LawyerProfileProps {
  lawyerId: string;
}

export function LawyerProfile({ lawyerId }: LawyerProfileProps) {
  const { user } = useAuth();
  const [lawyer, setLawyer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [appointmentData, setAppointmentData] = useState(null);

  useEffect(() => {
    loadLawyerProfile();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lawyerId]);

  const loadLawyerProfile = async () => {
    try {
      setLoading(true);
      
      const lawyerDoc = await getDoc(doc(db, 'lawyers', lawyerId));
      
      if (lawyerDoc.exists()) {
        setLawyer({ id: lawyerDoc.id, ...lawyerDoc.data() });
      } else {
        // Mock data for demonstration
        const mockLawyer = {
          id: lawyerId,
          firstName: 'María',
          lastName: 'González',
          specialty: 'family',
          specialtyName: 'Derecho de Familia',
          rating: 4.9,
          reviews: 127,
          location: 'San Juan, PR',
          description: 'Especialista en derecho de familia con más de 15 años de experiencia. Casos exitosos en divorcios, custodia y adopción. Comprometida con brindar asesoría legal compasiva y efectiva.',
          phone: '(*************',
          email: '<EMAIL>',
          price: '$150',
          priceType: 'hora',
          languages: ['Español', 'Inglés'],
          education: [
            'JD, Universidad de Puerto Rico - Escuela de Derecho (2008)',
            'BA en Ciencias Políticas, Universidad de Puerto Rico (2005)'
          ],
          experience: '15+ años',
          certifications: [
            'Colegio de Abogados de Puerto Rico',
            'Certificación en Mediación Familiar',
            'Especialización en Derecho de Menores'
          ],
          services: [
            'Divorcios y Separaciones',
            'Custodia de Menores',
            'Adopción',
            'Pensión Alimentaria',
            'Violencia Doméstica',
            'Mediación Familiar'
          ],
          availability: {
            monday: '9:00 AM - 5:00 PM',
            tuesday: '9:00 AM - 5:00 PM',
            wednesday: '9:00 AM - 5:00 PM',
            thursday: '9:00 AM - 5:00 PM',
            friday: '9:00 AM - 3:00 PM',
            saturday: 'Cerrado',
            sunday: 'Cerrado'
          },
          reviews: [
            {
              id: 1,
              clientName: 'Ana R.',
              rating: 5,
              comment: 'Excelente abogada, muy profesional y comprensiva. Me ayudó mucho en mi proceso de divorcio.',
              date: '2024-01-15'
            },
            {
              id: 2,
              clientName: 'Carlos M.',
              rating: 5,
              comment: 'Altamente recomendada. Su experiencia en custodia de menores es excepcional.',
              date: '2024-01-10'
            },
            {
              id: 3,
              clientName: 'Isabel L.',
              rating: 4,
              comment: 'Muy buena comunicación y resultados satisfactorios. Gracias por todo.',
              date: '2024-01-05'
            }
          ]
        };
        setLawyer(mockLawyer);
      }
    } catch (error) {
      console.error('Error loading lawyer profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBookConsultation = async () => {
    if (!user) {
      // Redirect to login
      window.location.href = '/es/auth/signin';
      return;
    }

    // Create appointment record first
    try {
      const { addDoc, collection, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');

      const appointmentDoc = await addDoc(collection(db, 'appointments'), {
        clientId: user.id,
        lawyerId: lawyer.id,
        clientName: `${user.firstName} ${user.lastName}`,
        clientEmail: user.email,
        clientPhone: user.phone || '',
        lawyerName: `${lawyer.firstName} ${lawyer.lastName}`,
        specialty: lawyer.specialtyName,
        status: 'payment_pending',
        paymentStatus: 'pending',
        amount: parseFloat(lawyer.price) || 150,
        date: new Date().toISOString(), // This would be selected by user in real implementation
        time: '10:00 AM', // This would be selected by user in real implementation
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      setAppointmentData({
        id: appointmentDoc.id,
        date: new Date().toLocaleDateString('es-ES'),
        time: '10:00 AM',
        amount: parseFloat(lawyer.price) || 150
      });

      setShowPaymentModal(true);
    } catch (error) {
      console.error('Error creating appointment:', error);
      alert('Error al crear la cita. Intenta de nuevo.');
    }
  };

  const handleSendMessage = () => {
    if (!user) {
      // Redirect to login
      window.location.href = '/es/auth/signin';
      return;
    }
    setShowMessageModal(true);
  };

  const handlePaymentSuccess = (paymentId: string) => {
    setShowPaymentModal(false);
    // Redirect to appointments page
    window.location.href = '/es/dashboard/client/appointments';
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    alert(`Error en el pago: ${error}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
          <p className="text-gray-400 mt-4 text-lg">Cargando perfil...</p>
        </div>
      </div>
    );
  }

  if (!lawyer) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Abogado no encontrado</h2>
          <Link href="/es/lawyers">
            <Button className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold">
              Volver a Búsqueda
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
      </div>

      <div className="relative z-10 p-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-6">
            <Link href="/es/lawyers">
              <Button className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-4 py-2 rounded-xl">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent">
                {lawyer.firstName} {lawyer.lastName}
              </h1>
              <p className="mt-2 text-gray-300 text-lg">
                {lawyer.specialtyName} • {lawyer.location}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Button className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-4 py-2 rounded-xl">
                <Heart className="h-5 w-5" />
              </Button>
              <Button className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-4 py-2 rounded-xl">
                <Share className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Profile Overview */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl">
              <CardContent className="p-8">
                <div className="flex items-start space-x-6">
                  <div className="w-24 h-24 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                    <Briefcase className="h-12 w-12 text-yellow-400" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="flex items-center space-x-1">
                        <Star className="h-6 w-6 text-yellow-400 fill-current" />
                        <span className="text-2xl font-bold text-white">{lawyer.rating}</span>
                        <span className="text-gray-400">({lawyer.reviews} reseñas)</span>
                      </div>
                      <Badge className="bg-gradient-to-r from-green-400/20 to-green-500/20 border border-green-400/30 text-green-400">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Verificado
                      </Badge>
                    </div>
                    
                    <p className="text-gray-300 text-lg leading-relaxed mb-6">
                      {lawyer.description}
                    </p>
                    
                    <div className="grid grid-cols-2 gap-6 text-sm">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-5 w-5 text-gray-500" />
                        <span className="text-gray-300">Experiencia: {lawyer.experience}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5 text-gray-500" />
                        <span className="text-gray-300">Idiomas: {lawyer.languages?.join(', ')}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-5 w-5 text-gray-500" />
                        <span className="text-gray-300">{lawyer.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-gray-500" />
                        <span className="text-gray-300">{lawyer.price}/{lawyer.priceType}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Services */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
              <CardHeader className="p-8 border-b border-white/10">
                <CardTitle className="text-white text-xl">Servicios Legales</CardTitle>
                <CardDescription className="text-gray-400">
                  Áreas de especialización y servicios ofrecidos
                </CardDescription>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {lawyer.services?.map((service, index) => (
                    <div key={index} className="flex items-center space-x-3 p-4 bg-white/5 rounded-2xl border border-white/10">
                      <CheckCircle className="h-5 w-5 text-green-400" />
                      <span className="text-white font-medium">{service}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Education & Certifications */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
              <CardHeader className="p-8 border-b border-white/10">
                <CardTitle className="text-white text-xl">Educación y Certificaciones</CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                      <GraduationCap className="h-5 w-5 mr-2 text-blue-400" />
                      Educación
                    </h4>
                    <div className="space-y-2">
                      {lawyer.education?.map((edu, index) => (
                        <p key={index} className="text-gray-300 pl-7">{edu}</p>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                      <Award className="h-5 w-5 mr-2 text-yellow-400" />
                      Certificaciones
                    </h4>
                    <div className="space-y-2">
                      {lawyer.certifications?.map((cert, index) => (
                        <p key={index} className="text-gray-300 pl-7">{cert}</p>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Reviews */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
              <CardHeader className="p-8 border-b border-white/10">
                <CardTitle className="text-white text-xl">Reseñas de Clientes</CardTitle>
                <CardDescription className="text-gray-400">
                  Lo que dicen nuestros clientes
                </CardDescription>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-6">
                  {lawyer.reviews?.slice(0, 3).map((review) => (
                    <div key={review.id} className="p-6 bg-white/5 rounded-2xl border border-white/10">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-full flex items-center justify-center">
                            <Users className="h-5 w-5 text-blue-400" />
                          </div>
                          <div>
                            <p className="font-semibold text-white">{review.clientName}</p>
                            <p className="text-sm text-gray-400">{review.date}</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}`} />
                          ))}
                        </div>
                      </div>
                      <p className="text-gray-300">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Actions */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl sticky top-8">
              <CardContent className="p-8">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-white mb-2">{lawyer.price}</div>
                  <p className="text-gray-400">por {lawyer.priceType}</p>
                </div>
                
                <div className="space-y-4">
                  <Button 
                    onClick={handleBookConsultation}
                    className="w-full bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold py-4 rounded-2xl text-lg"
                  >
                    <Calendar className="mr-2 h-5 w-5" />
                    Agendar Consulta
                  </Button>
                  
                  <Button 
                    onClick={handleSendMessage}
                    className="w-full bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 hover:border-green-500/50 py-4 rounded-2xl font-semibold"
                  >
                    <MessageSquare className="mr-2 h-5 w-5" />
                    Enviar Mensaje
                  </Button>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <Button className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 hover:border-blue-500/50 py-3 rounded-xl">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button className="bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-500/30 text-purple-400 hover:bg-purple-500/30 hover:border-purple-500/50 py-3 rounded-xl">
                      <Mail className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Availability */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
              <CardHeader className="p-6 border-b border-white/10">
                <CardTitle className="text-white text-lg">Horarios de Atención</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-3 text-sm">
                  {Object.entries(lawyer.availability || {}).map(([day, hours]) => (
                    <div key={day} className="flex justify-between">
                      <span className="text-gray-400 capitalize">{day}:</span>
                      <span className="text-white">{hours}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
              <CardContent className="p-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-white">{lawyer.reviews}</div>
                    <div className="text-sm text-gray-400">Reseñas</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">{lawyer.experience}</div>
                    <div className="text-sm text-gray-400">Experiencia</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl max-w-md w-full">
            <CardHeader className="p-6">
              <CardTitle className="text-white">Agendar Consulta</CardTitle>
              <CardDescription className="text-gray-400">
                Próximamente podrás agendar citas directamente
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <Calendar className="h-16 w-16 text-yellow-400 mx-auto mb-4" />
                <p className="text-gray-300 mb-6">
                  El sistema de agendamiento estará disponible pronto
                </p>
                <Button 
                  onClick={() => setShowBookingModal(false)}
                  className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold"
                >
                  Cerrar
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Message Modal */}
      {showMessageModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-green-500/20 rounded-3xl max-w-md w-full">
            <CardHeader className="p-6">
              <CardTitle className="text-white">Enviar Mensaje</CardTitle>
              <CardDescription className="text-gray-400">
                Próximamente podrás enviar mensajes directamente
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <MessageSquare className="h-16 w-16 text-green-400 mx-auto mb-4" />
                <p className="text-gray-300 mb-6">
                  El sistema de mensajería estará disponible pronto
                </p>
                <Button 
                  onClick={() => setShowMessageModal(false)}
                  className="bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:via-green-600 hover:to-green-700 text-black font-bold"
                >
                  Cerrar
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Payment Modal */}
      {showPaymentModal && appointmentData && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={appointmentData.amount}
          lawyerId={lawyer.id}
          lawyerName={`${lawyer.firstName} ${lawyer.lastName}`}
          appointmentId={appointmentData.id}
          appointmentDate={appointmentData.date}
          appointmentTime={appointmentData.time}
          specialty={lawyer.specialtyName}
          description={`Consulta legal - ${lawyer.specialtyName}`}
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
        />
      )}
    </div>
  );
}
