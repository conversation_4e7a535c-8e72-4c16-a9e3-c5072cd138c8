import type { Metada<PERSON>, Viewport } from 'next';
import { Inter } from 'next/font/google';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { AuthProvider } from '@/components/providers/auth-provider';
import { Toaster } from '@/components/ui/toaster';
import { InstallPrompt } from '@/components/ui/install-prompt';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Abogo - Puerto Rico Legal Marketplace',
  description: 'Conectamos a personas con abogados expertos en todas las áreas del derecho en Puerto Rico. Rápido, seguro y sin complicaciones.',
  keywords: 'Puerto Rico, abogados, servicios legales, bufete, marketplace legal, derecho',
  authors: [{ name: 'Abogo Team' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  // themeColor is now handled in the viewport export below
  openGraph: {
    title: 'Abogo - Puerto Rico Legal Marketplace',
    description: 'Conectamos a personas con abogados expertos en todas las áreas del derecho en Puerto Rico.',
    url: '/',
    siteName: 'Abogo',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Abogo - Puerto Rico Legal Marketplace',
      },
    ],
    locale: 'es_PR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Abogo - Puerto Rico Legal Marketplace',
    description: 'Conectamos a personas con abogados expertos en todas las áreas del derecho en Puerto Rico.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Abogo',
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'Abogo',
  },
};

export const viewport: Viewport = {
  themeColor: '#f59e0b',
  colorScheme: 'light dark',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
};

interface RootLayoutProps {
  children: React.ReactNode;
  params?: { locale?: string };
}

export default async function RootLayout({
  children,
  params
}: RootLayoutProps) {
  const locale = params?.locale || 'es';
  const messages = await getMessages();

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={inter.className}>
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange
          >
            {children}
            <Toaster />
            <InstallPrompt />
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
