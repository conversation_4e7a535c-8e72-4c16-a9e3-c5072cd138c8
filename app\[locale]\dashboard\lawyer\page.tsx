import { setRequestLocale } from 'next-intl/server';
import LawyerDashboardPage from '@/components/pages/lawyer-dashboard-page';

// Force dynamic rendering to prevent Firebase initialization during build
export const dynamic = 'force-dynamic';

interface LawyerDashboardPageProps {
  params: { locale: string };
}

export default function LawyerDashboard({ params: { locale } }: LawyerDashboardPageProps) {
  setRequestLocale(locale);
  
  return <LawyerDashboardPage />;
}
