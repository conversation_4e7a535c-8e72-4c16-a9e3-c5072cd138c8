'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { NotificationBell } from '@/components/notifications/notification-bell';
import {
  Calendar,
  MessageSquare,
  Search,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  User,
  LogOut,
  Scale,
  MapPin,
  Phone,
  Mail,
  Briefcase,
  Bell,
  Users,
  DollarSign,
  TrendingUp,
  Eye,
  Settings
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function LawyerDashboard() {
  const { user, logout } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [messages, setMessages] = useState([]);
  const [clients, setClients] = useState([]);
  const [earnings, setEarnings] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load lawyer's appointments
      const appointmentsQuery = query(
        collection(db, 'appointments'),
        where('lawyerId', '==', user.id),
        orderBy('date', 'desc'),
        limit(5)
      );
      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      const appointmentsData = appointmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setAppointments(appointmentsData);

      // Load recent messages
      const messagesQuery = query(
        collection(db, 'messages'),
        where('lawyerId', '==', user.id),
        orderBy('timestamp', 'desc'),
        limit(5)
      );
      const messagesSnapshot = await getDocs(messagesQuery);
      const messagesData = messagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setMessages(messagesData);

      // Load clients who have booked appointments
      const clientsQuery = query(
        collection(db, 'appointments'),
        where('lawyerId', '==', user.id),
        orderBy('date', 'desc')
      );
      const clientsSnapshot = await getDocs(clientsQuery);
      const uniqueClients = new Set();
      clientsSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.clientId) {
          uniqueClients.add(data.clientId);
        }
      });
      setClients(Array.from(uniqueClients));

      // Calculate earnings (mock data for now)
      setEarnings(appointmentsData.length * 150); // $150 per appointment

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
      </div>

      <div className="relative z-10 p-8">
        {/* Top Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar clientes, citas, o casos..."
              className="w-full pl-12 pr-4 py-4 bg-black/40 backdrop-blur-xl border border-yellow-500/30 rounded-2xl text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 transition-all duration-300"
            />
            <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-semibold px-6 py-2 rounded-xl shadow-lg hover:shadow-yellow-500/25 transition-all duration-300">
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Header */}
        <div className="flex justify-between items-center mb-10">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent">
              ¡Bienvenido/a, {user?.firstName}!
            </h1>
            <p className="mt-3 text-gray-300 text-lg">
              Gestiona tu práctica legal y conecta con nuevos clientes en Puerto Rico.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <NotificationBell />
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Total Clients */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-blue-500/20 hover:border-blue-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-7 w-7 text-blue-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-blue-500 bg-clip-text text-transparent">{clients.length}</div>
                  <p className="text-xs text-gray-400 font-medium">Clientes</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Mis Clientes</h3>
              <p className="text-sm text-gray-400 mb-6">Clientes que han agendado citas</p>
              <Button className="w-full bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 hover:border-blue-500/50 font-bold py-3 rounded-2xl transition-all duration-300 group-hover:scale-105">
                <Users className="mr-2 h-4 w-4" />
                Ver Clientes
              </Button>
            </CardContent>
          </Card>

          {/* Appointments */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 hover:border-yellow-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-yellow-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Calendar className="h-7 w-7 text-yellow-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent">{appointments.length}</div>
                  <p className="text-xs text-gray-400 font-medium">Este Mes</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Citas</h3>
              <p className="text-sm text-gray-400 mb-6">Consultas programadas</p>
              <Button className="w-full bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold py-3 rounded-2xl shadow-lg hover:shadow-yellow-500/25 transition-all duration-300 group-hover:scale-105">
                <Calendar className="mr-2 h-4 w-4" />
                Gestionar Citas
              </Button>
            </CardContent>
          </Card>

          {/* Messages */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-green-500/20 hover:border-green-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-green-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <MessageSquare className="h-7 w-7 text-green-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-green-500 bg-clip-text text-transparent">{messages.length}</div>
                  <p className="text-xs text-gray-400 font-medium">Nuevos</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Mensajes</h3>
              <p className="text-sm text-gray-400 mb-6">Comunicación con clientes</p>
              <Button className="w-full bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 hover:border-green-500/50 font-bold py-3 rounded-2xl transition-all duration-300 group-hover:scale-105">
                <MessageSquare className="mr-2 h-4 w-4" />
                Ver Mensajes
              </Button>
            </CardContent>
          </Card>

          {/* Earnings */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-purple-500/20 hover:border-purple-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="h-7 w-7 text-purple-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-purple-500 bg-clip-text text-transparent">${earnings}</div>
                  <p className="text-xs text-gray-400 font-medium">Este Mes</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Ingresos</h3>
              <p className="text-sm text-gray-400 mb-6">Ganancias mensuales</p>
              <Button className="w-full bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-500/30 text-purple-400 hover:bg-purple-500/30 hover:border-purple-500/50 font-bold py-3 rounded-2xl transition-all duration-300 group-hover:scale-105">
                <TrendingUp className="mr-2 h-4 w-4" />
                Ver Reportes
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>


