'use client';

import React from 'react';
import { useAuth } from '@/components/providers/auth-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  MessageSquare, 
  DollarSign, 
  Star, 
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings
} from 'lucide-react';

export function LawyerDashboard() {
  const { user } = useAuth();

  return (
    <div className="p-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          ¡Bienvenido/a, {user?.firstName}!
        </h1>
        <p className="mt-2 text-gray-600">
          Gestiona tu práctica legal y conecta con nuevos clientes.
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Citas Hoy</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              +2 desde ayer
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ingresos del Mes</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$4,250</div>
            <p className="text-xs text-muted-foreground">
              +15% desde el mes pasado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Calificación</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.9</div>
            <p className="text-xs text-muted-foreground">
              Basado en 47 reseñas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Nuevos Clientes</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              Este mes
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Schedule */}
        <Card>
          <CardHeader>
            <CardTitle>Agenda de Hoy</CardTitle>
            <CardDescription>
              Tus citas programadas para hoy
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 border rounded-lg bg-blue-50">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Consulta con Juan Pérez
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho de Familia • 9:00 AM - 10:00 AM
                  </p>
                  <p className="text-xs text-gray-400">
                    Divorcio y custodia de menores
                  </p>
                </div>
                <Button size="sm" variant="legal">
                  Iniciar
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-gray-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Consulta con María López
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho Inmobiliario • 11:30 AM - 12:30 PM
                  </p>
                  <p className="text-xs text-gray-400">
                    Compra de propiedad
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  Ver Detalles
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-gray-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Consulta con Carlos Rivera
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho Laboral • 2:00 PM - 3:00 PM
                  </p>
                  <p className="text-xs text-gray-400">
                    Despido injustificado
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  Ver Detalles
                </Button>
              </div>
            </div>

            <Button className="w-full mt-4" variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Ver Calendario Completo
            </Button>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Actividad Reciente</CardTitle>
            <CardDescription>
              Últimas acciones en tu cuenta
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Nueva reseña recibida
                  </p>
                  <p className="text-sm text-gray-500">
                    Ana Martínez te dio 5 estrellas
                  </p>
                  <p className="text-xs text-gray-400">
                    Hace 2 horas
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-8 w-8 text-blue-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Nuevo mensaje
                  </p>
                  <p className="text-sm text-gray-500">
                    Luis García envió una consulta
                  </p>
                  <p className="text-xs text-gray-400">
                    Hace 4 horas
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Pago recibido
                  </p>
                  <p className="text-sm text-gray-500">
                    $300 por consulta completada
                  </p>
                  <p className="text-xs text-gray-400">
                    Ayer
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-8 w-8 text-orange-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Perfil incompleto
                  </p>
                  <p className="text-sm text-gray-500">
                    Completa tu perfil para más visibilidad
                  </p>
                  <p className="text-xs text-gray-400">
                    Hace 2 días
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Button className="w-full mt-4" variant="outline">
              Ver Toda la Actividad
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Acciones Rápidas</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button variant="legal" className="h-16">
            <Calendar className="mr-2 h-5 w-5" />
            Gestionar Disponibilidad
          </Button>
          <Button variant="outline" className="h-16">
            <TrendingUp className="mr-2 h-5 w-5" />
            Ver Estadísticas
          </Button>
          <Button variant="outline" className="h-16">
            <Settings className="mr-2 h-5 w-5" />
            Configurar Perfil
          </Button>
        </div>
      </div>
    </div>
  );
}
