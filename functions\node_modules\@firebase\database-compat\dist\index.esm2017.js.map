{"version": 3, "file": "index.esm2017.js", "sources": ["../src/util/util.ts", "../src/util/validation.ts", "../src/api/onDisconnect.ts", "../src/api/TransactionResult.ts", "../src/api/Reference.ts", "../src/api/Database.ts", "../src/api/internal.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nconst logClient = new Logger('@firebase/database-compat');\n\nexport const warn = function (msg: string) {\n  const message = 'FIREBASE WARNING: ' + msg;\n  logClient.warn(message);\n};\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { errorPrefix as errorPrefixFxn } from '@firebase/util';\n\nexport const validateBoolean = function (\n  fnName: string,\n  argumentName: string,\n  bool: unknown,\n  optional: boolean\n) {\n  if (optional && bool === undefined) {\n    return;\n  }\n  if (typeof bool !== 'boolean') {\n    throw new Error(\n      errorPrefixFxn(fnName, argumentName) + 'must be a boolean.'\n    );\n  }\n};\n\nexport const validateEventType = function (\n  fnName: string,\n  eventType: string,\n  optional: boolean\n) {\n  if (optional && eventType === undefined) {\n    return;\n  }\n\n  switch (eventType) {\n    case 'value':\n    case 'child_added':\n    case 'child_removed':\n    case 'child_changed':\n    case 'child_moved':\n      break;\n    default:\n      throw new Error(\n        errorPrefixFxn(fnName, 'eventType') +\n          'must be a valid event type = \"value\", \"child_added\", \"child_removed\", ' +\n          '\"child_changed\", or \"child_moved\".'\n      );\n  }\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OnDisconnect as ModularOnDisconnect } from '@firebase/database';\nimport { validateArgCount, validateCallback, Compat } from '@firebase/util';\n\nimport { warn } from '../util/util';\nexport class OnDisconnect implements Compat<ModularOnDisconnect> {\n  constructor(readonly _delegate: ModularOnDisconnect) {}\n\n  cancel(onComplete?: (a: Error | null) => void): Promise<void> {\n    validateArgCount('OnDisconnect.cancel', 0, 1, arguments.length);\n    validateCallback('OnDisconnect.cancel', 'onComplete', onComplete, true);\n    const result = this._delegate.cancel();\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  remove(onComplete?: (a: Error | null) => void): Promise<void> {\n    validateArgCount('OnDisconnect.remove', 0, 1, arguments.length);\n    validateCallback('OnDisconnect.remove', 'onComplete', onComplete, true);\n    const result = this._delegate.remove();\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  set(value: unknown, onComplete?: (a: Error | null) => void): Promise<void> {\n    validateArgCount('OnDisconnect.set', 1, 2, arguments.length);\n    validateCallback('OnDisconnect.set', 'onComplete', onComplete, true);\n    const result = this._delegate.set(value);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  setWithPriority(\n    value: unknown,\n    priority: number | string | null,\n    onComplete?: (a: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('OnDisconnect.setWithPriority', 2, 3, arguments.length);\n    validateCallback(\n      'OnDisconnect.setWithPriority',\n      'onComplete',\n      onComplete,\n      true\n    );\n    const result = this._delegate.setWithPriority(value, priority);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  update(\n    objectToMerge: Record<string, unknown>,\n    onComplete?: (a: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('OnDisconnect.update', 1, 2, arguments.length);\n    if (Array.isArray(objectToMerge)) {\n      const newObjectToMerge: { [k: string]: unknown } = {};\n      for (let i = 0; i < objectToMerge.length; ++i) {\n        newObjectToMerge['' + i] = objectToMerge[i];\n      }\n      objectToMerge = newObjectToMerge;\n      warn(\n        'Passing an Array to firebase.database.onDisconnect().update() is deprecated. Use set() if you want to overwrite the ' +\n          'existing data, or an Object with integer keys if you really do want to only update some of the children.'\n      );\n    }\n    validateCallback('OnDisconnect.update', 'onComplete', onComplete, true);\n    const result = this._delegate.update(objectToMerge);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { validateArgCount } from '@firebase/util';\n\nimport { DataSnapshot } from './Reference';\n\nexport class TransactionResult {\n  /**\n   * A type for the resolve value of Firebase.transaction.\n   */\n  constructor(public committed: boolean, public snapshot: DataSnapshot) {}\n\n  // Do not create public documentation. This is intended to make JSON serialization work but is otherwise unnecessary\n  // for end-users\n  toJSON(): object {\n    validateArgCount('TransactionResult.toJSON', 0, 1, arguments.length);\n    return { committed: this.committed, snapshot: this.snapshot.toJSON() };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OnDisconnect as ModularOnDisconnect,\n  off,\n  onChildAdded,\n  onChildChanged,\n  onChildMoved,\n  onChildRemoved,\n  onValue,\n  EventType,\n  limitToFirst,\n  query,\n  limitToLast,\n  orderByChild,\n  orderByKey,\n  orderByValue,\n  orderByPriority,\n  startAt,\n  startAfter,\n  endAt,\n  endBefore,\n  equalTo,\n  get,\n  set,\n  update,\n  setWithPriority,\n  remove,\n  setPriority,\n  push,\n  runTransaction,\n  child,\n  DataSnapshot as ModularDataSnapshot,\n  Query as ExpQuery,\n  DatabaseReference as Modu<PERSON><PERSON>eference,\n  _QueryImpl,\n  _ReferenceImpl,\n  _validatePathString,\n  _validateWritablePath,\n  _UserCallback,\n  _QueryParams\n} from '@firebase/database';\nimport {\n  Compat,\n  Deferred,\n  errorPrefix,\n  validateArgCount,\n  validateCallback,\n  validateContextObject\n} from '@firebase/util';\n\nimport { warn } from '../util/util';\nimport { validateBoolean, validateEventType } from '../util/validation';\n\nimport { Database } from './Database';\nimport { OnDisconnect } from './onDisconnect';\nimport { TransactionResult } from './TransactionResult';\n\n/**\n * Class representing a firebase data snapshot.  It wraps a SnapshotNode and\n * surfaces the public methods (val, forEach, etc.) we want to expose.\n */\nexport class DataSnapshot implements Compat<ModularDataSnapshot> {\n  constructor(\n    readonly _database: Database,\n    readonly _delegate: ModularDataSnapshot\n  ) {}\n\n  /**\n   * Retrieves the snapshot contents as JSON.  Returns null if the snapshot is\n   * empty.\n   *\n   * @returns JSON representation of the DataSnapshot contents, or null if empty.\n   */\n  val(): unknown {\n    validateArgCount('DataSnapshot.val', 0, 0, arguments.length);\n    return this._delegate.val();\n  }\n\n  /**\n   * Returns the snapshot contents as JSON, including priorities of node.  Suitable for exporting\n   * the entire node contents.\n   * @returns JSON representation of the DataSnapshot contents, or null if empty.\n   */\n  exportVal(): unknown {\n    validateArgCount('DataSnapshot.exportVal', 0, 0, arguments.length);\n    return this._delegate.exportVal();\n  }\n\n  // Do not create public documentation. This is intended to make JSON serialization work but is otherwise unnecessary\n  // for end-users\n  toJSON(): unknown {\n    // Optional spacer argument is unnecessary because we're depending on recursion rather than stringifying the content\n    validateArgCount('DataSnapshot.toJSON', 0, 1, arguments.length);\n    return this._delegate.toJSON();\n  }\n\n  /**\n   * Returns whether the snapshot contains a non-null value.\n   *\n   * @returns Whether the snapshot contains a non-null value, or is empty.\n   */\n  exists(): boolean {\n    validateArgCount('DataSnapshot.exists', 0, 0, arguments.length);\n    return this._delegate.exists();\n  }\n\n  /**\n   * Returns a DataSnapshot of the specified child node's contents.\n   *\n   * @param path - Path to a child.\n   * @returns DataSnapshot for child node.\n   */\n  child(path: string): DataSnapshot {\n    validateArgCount('DataSnapshot.child', 0, 1, arguments.length);\n    // Ensure the childPath is a string (can be a number)\n    path = String(path);\n    _validatePathString('DataSnapshot.child', 'path', path, false);\n    return new DataSnapshot(this._database, this._delegate.child(path));\n  }\n\n  /**\n   * Returns whether the snapshot contains a child at the specified path.\n   *\n   * @param path - Path to a child.\n   * @returns Whether the child exists.\n   */\n  hasChild(path: string): boolean {\n    validateArgCount('DataSnapshot.hasChild', 1, 1, arguments.length);\n    _validatePathString('DataSnapshot.hasChild', 'path', path, false);\n    return this._delegate.hasChild(path);\n  }\n\n  /**\n   * Returns the priority of the object, or null if no priority was set.\n   *\n   * @returns The priority.\n   */\n  getPriority(): string | number | null {\n    validateArgCount('DataSnapshot.getPriority', 0, 0, arguments.length);\n    return this._delegate.priority;\n  }\n\n  /**\n   * Iterates through child nodes and calls the specified action for each one.\n   *\n   * @param action - Callback function to be called\n   * for each child.\n   * @returns True if forEach was canceled by action returning true for\n   * one of the child nodes.\n   */\n  forEach(action: (snapshot: IteratedDataSnapshot) => boolean | void): boolean {\n    validateArgCount('DataSnapshot.forEach', 1, 1, arguments.length);\n    validateCallback('DataSnapshot.forEach', 'action', action, false);\n    return this._delegate.forEach(expDataSnapshot =>\n      action(new DataSnapshot(this._database, expDataSnapshot))\n    );\n  }\n\n  /**\n   * Returns whether this DataSnapshot has children.\n   * @returns True if the DataSnapshot contains 1 or more child nodes.\n   */\n  hasChildren(): boolean {\n    validateArgCount('DataSnapshot.hasChildren', 0, 0, arguments.length);\n    return this._delegate.hasChildren();\n  }\n\n  get key() {\n    return this._delegate.key;\n  }\n\n  /**\n   * Returns the number of children for this DataSnapshot.\n   * @returns The number of children that this DataSnapshot contains.\n   */\n  numChildren(): number {\n    validateArgCount('DataSnapshot.numChildren', 0, 0, arguments.length);\n    return this._delegate.size;\n  }\n\n  /**\n   * @returns The Firebase reference for the location this snapshot's data came\n   * from.\n   */\n  getRef(): Reference {\n    validateArgCount('DataSnapshot.ref', 0, 0, arguments.length);\n    return new Reference(this._database, this._delegate.ref);\n  }\n\n  get ref(): Reference {\n    return this.getRef();\n  }\n}\n\n/**\n * Represents a child snapshot of a `Reference` that is being iterated over. The key will never be undefined.\n */\nexport interface IteratedDataSnapshot extends DataSnapshot {\n  key: string; // key of the location of this snapshot.\n}\n\nexport interface SnapshotCallback {\n  (dataSnapshot: DataSnapshot, previousChildName?: string | null): unknown;\n}\n\n/**\n * A Query represents a filter to be applied to a firebase location.  This object purely represents the\n * query expression (and exposes our public API to build the query).  The actual query logic is in ViewBase.js.\n *\n * Since every Firebase reference is a query, Firebase inherits from this object.\n */\nexport class Query implements Compat<ExpQuery> {\n  constructor(readonly database: Database, readonly _delegate: ExpQuery) {}\n\n  on(\n    eventType: string,\n    callback: SnapshotCallback,\n    cancelCallbackOrContext?: ((a: Error) => unknown) | object | null,\n    context?: object | null\n  ): SnapshotCallback {\n    validateArgCount('Query.on', 2, 4, arguments.length);\n    validateCallback('Query.on', 'callback', callback, false);\n\n    const ret = Query.getCancelAndContextArgs_(\n      'Query.on',\n      cancelCallbackOrContext,\n      context\n    );\n    const valueCallback = (expSnapshot, previousChildName?) => {\n      callback.call(\n        ret.context,\n        new DataSnapshot(this.database, expSnapshot),\n        previousChildName\n      );\n    };\n    valueCallback.userCallback = callback;\n    valueCallback.context = ret.context;\n    const cancelCallback = ret.cancel?.bind(ret.context);\n\n    switch (eventType) {\n      case 'value':\n        onValue(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_added':\n        onChildAdded(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_removed':\n        onChildRemoved(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_changed':\n        onChildChanged(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      case 'child_moved':\n        onChildMoved(this._delegate, valueCallback, cancelCallback);\n        return callback;\n      default:\n        throw new Error(\n          errorPrefix('Query.on', 'eventType') +\n            'must be a valid event type = \"value\", \"child_added\", \"child_removed\", ' +\n            '\"child_changed\", or \"child_moved\".'\n        );\n    }\n  }\n\n  off(\n    eventType?: string,\n    callback?: SnapshotCallback,\n    context?: object | null\n  ): void {\n    validateArgCount('Query.off', 0, 3, arguments.length);\n    validateEventType('Query.off', eventType, true);\n    validateCallback('Query.off', 'callback', callback, true);\n    validateContextObject('Query.off', 'context', context, true);\n    if (callback) {\n      const valueCallback: _UserCallback = () => {};\n      valueCallback.userCallback = callback;\n      valueCallback.context = context;\n      off(this._delegate, eventType as EventType, valueCallback);\n    } else {\n      off(this._delegate, eventType as EventType | undefined);\n    }\n  }\n\n  /**\n   * Get the server-value for this query, or return a cached value if not connected.\n   */\n  get(): Promise<DataSnapshot> {\n    return get(this._delegate).then(expSnapshot => {\n      return new DataSnapshot(this.database, expSnapshot);\n    });\n  }\n\n  /**\n   * Attaches a listener, waits for the first event, and then removes the listener\n   */\n  once(\n    eventType: string,\n    callback?: SnapshotCallback,\n    failureCallbackOrContext?: ((a: Error) => void) | object | null,\n    context?: object | null\n  ): Promise<DataSnapshot> {\n    validateArgCount('Query.once', 1, 4, arguments.length);\n    validateCallback('Query.once', 'callback', callback, true);\n\n    const ret = Query.getCancelAndContextArgs_(\n      'Query.once',\n      failureCallbackOrContext,\n      context\n    );\n    const deferred = new Deferred<DataSnapshot>();\n    const valueCallback: _UserCallback = (expSnapshot, previousChildName?) => {\n      const result = new DataSnapshot(this.database, expSnapshot);\n      if (callback) {\n        callback.call(ret.context, result, previousChildName);\n      }\n      deferred.resolve(result);\n    };\n    valueCallback.userCallback = callback;\n    valueCallback.context = ret.context;\n    const cancelCallback = (error: Error) => {\n      if (ret.cancel) {\n        ret.cancel.call(ret.context, error);\n      }\n      deferred.reject(error);\n    };\n\n    switch (eventType) {\n      case 'value':\n        onValue(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_added':\n        onChildAdded(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_removed':\n        onChildRemoved(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_changed':\n        onChildChanged(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      case 'child_moved':\n        onChildMoved(this._delegate, valueCallback, cancelCallback, {\n          onlyOnce: true\n        });\n        break;\n      default:\n        throw new Error(\n          errorPrefix('Query.once', 'eventType') +\n            'must be a valid event type = \"value\", \"child_added\", \"child_removed\", ' +\n            '\"child_changed\", or \"child_moved\".'\n        );\n    }\n\n    return deferred.promise;\n  }\n\n  /**\n   * Set a limit and anchor it to the start of the window.\n   */\n  limitToFirst(limit: number): Query {\n    validateArgCount('Query.limitToFirst', 1, 1, arguments.length);\n    return new Query(this.database, query(this._delegate, limitToFirst(limit)));\n  }\n\n  /**\n   * Set a limit and anchor it to the end of the window.\n   */\n  limitToLast(limit: number): Query {\n    validateArgCount('Query.limitToLast', 1, 1, arguments.length);\n    return new Query(this.database, query(this._delegate, limitToLast(limit)));\n  }\n\n  /**\n   * Given a child path, return a new query ordered by the specified grandchild path.\n   */\n  orderByChild(path: string): Query {\n    validateArgCount('Query.orderByChild', 1, 1, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByChild(path)));\n  }\n\n  /**\n   * Return a new query ordered by the KeyIndex\n   */\n  orderByKey(): Query {\n    validateArgCount('Query.orderByKey', 0, 0, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByKey()));\n  }\n\n  /**\n   * Return a new query ordered by the PriorityIndex\n   */\n  orderByPriority(): Query {\n    validateArgCount('Query.orderByPriority', 0, 0, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByPriority()));\n  }\n\n  /**\n   * Return a new query ordered by the ValueIndex\n   */\n  orderByValue(): Query {\n    validateArgCount('Query.orderByValue', 0, 0, arguments.length);\n    return new Query(this.database, query(this._delegate, orderByValue()));\n  }\n\n  startAt(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.startAt', 0, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, startAt(value, name))\n    );\n  }\n\n  startAfter(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.startAfter', 0, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, startAfter(value, name))\n    );\n  }\n\n  endAt(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.endAt', 0, 2, arguments.length);\n    return new Query(this.database, query(this._delegate, endAt(value, name)));\n  }\n\n  endBefore(\n    value: number | string | boolean | null = null,\n    name?: string | null\n  ): Query {\n    validateArgCount('Query.endBefore', 0, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, endBefore(value, name))\n    );\n  }\n\n  /**\n   * Load the selection of children with exactly the specified value, and, optionally,\n   * the specified name.\n   */\n  equalTo(value: number | string | boolean | null, name?: string) {\n    validateArgCount('Query.equalTo', 1, 2, arguments.length);\n    return new Query(\n      this.database,\n      query(this._delegate, equalTo(value, name))\n    );\n  }\n\n  /**\n   * @returns URL for this location.\n   */\n  toString(): string {\n    validateArgCount('Query.toString', 0, 0, arguments.length);\n    return this._delegate.toString();\n  }\n\n  // Do not create public documentation. This is intended to make JSON serialization work but is otherwise unnecessary\n  // for end-users.\n  toJSON() {\n    // An optional spacer argument is unnecessary for a string.\n    validateArgCount('Query.toJSON', 0, 1, arguments.length);\n    return this._delegate.toJSON();\n  }\n\n  /**\n   * Return true if this query and the provided query are equivalent; otherwise, return false.\n   */\n  isEqual(other: Query): boolean {\n    validateArgCount('Query.isEqual', 1, 1, arguments.length);\n    if (!(other instanceof Query)) {\n      const error =\n        'Query.isEqual failed: First argument must be an instance of firebase.database.Query.';\n      throw new Error(error);\n    }\n    return this._delegate.isEqual(other._delegate);\n  }\n\n  /**\n   * Helper used by .on and .once to extract the context and or cancel arguments.\n   * @param fnName - The function name (on or once)\n   *\n   */\n  private static getCancelAndContextArgs_(\n    fnName: string,\n    cancelOrContext?: ((a: Error) => void) | object | null,\n    context?: object | null\n  ): { cancel: ((a: Error) => void) | undefined; context: object | undefined } {\n    const ret: {\n      cancel: ((a: Error) => void) | null;\n      context: object | null;\n    } = { cancel: undefined, context: undefined };\n    if (cancelOrContext && context) {\n      ret.cancel = cancelOrContext as (a: Error) => void;\n      validateCallback(fnName, 'cancel', ret.cancel, true);\n\n      ret.context = context;\n      validateContextObject(fnName, 'context', ret.context, true);\n    } else if (cancelOrContext) {\n      // we have either a cancel callback or a context.\n      if (typeof cancelOrContext === 'object' && cancelOrContext !== null) {\n        // it's a context!\n        ret.context = cancelOrContext;\n      } else if (typeof cancelOrContext === 'function') {\n        ret.cancel = cancelOrContext as (a: Error) => void;\n      } else {\n        throw new Error(\n          errorPrefix(fnName, 'cancelOrContext') +\n            ' must either be a cancel callback or a context object.'\n        );\n      }\n    }\n    return ret;\n  }\n\n  get ref(): Reference {\n    return new Reference(\n      this.database,\n      new _ReferenceImpl(this._delegate._repo, this._delegate._path)\n    );\n  }\n}\n\nexport class Reference extends Query implements Compat<ModularReference> {\n  then: Promise<Reference>['then'];\n  catch: Promise<Reference>['catch'];\n\n  /**\n   * Call options:\n   *   new Reference(Repo, Path) or\n   *   new Reference(url: string, string|RepoManager)\n   *\n   * Externally - this is the firebase.database.Reference type.\n   */\n  constructor(\n    readonly database: Database,\n    readonly _delegate: ModularReference\n  ) {\n    super(\n      database,\n      new _QueryImpl(\n        _delegate._repo,\n        _delegate._path,\n        new _QueryParams(),\n        false\n      )\n    );\n  }\n\n  /** @returns {?string} */\n  getKey(): string | null {\n    validateArgCount('Reference.key', 0, 0, arguments.length);\n    return this._delegate.key;\n  }\n\n  child(pathString: string): Reference {\n    validateArgCount('Reference.child', 1, 1, arguments.length);\n    if (typeof pathString === 'number') {\n      pathString = String(pathString);\n    }\n    return new Reference(this.database, child(this._delegate, pathString));\n  }\n\n  /** @returns {?Reference} */\n  getParent(): Reference | null {\n    validateArgCount('Reference.parent', 0, 0, arguments.length);\n    const parent = this._delegate.parent;\n    return parent ? new Reference(this.database, parent) : null;\n  }\n\n  /** @returns {!Reference} */\n  getRoot(): Reference {\n    validateArgCount('Reference.root', 0, 0, arguments.length);\n    return new Reference(this.database, this._delegate.root);\n  }\n\n  set(\n    newVal: unknown,\n    onComplete?: (error: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('Reference.set', 1, 2, arguments.length);\n    validateCallback('Reference.set', 'onComplete', onComplete, true);\n    const result = set(this._delegate, newVal);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  update(\n    values: object,\n    onComplete?: (a: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('Reference.update', 1, 2, arguments.length);\n\n    if (Array.isArray(values)) {\n      const newObjectToMerge: { [k: string]: unknown } = {};\n      for (let i = 0; i < values.length; ++i) {\n        newObjectToMerge['' + i] = values[i];\n      }\n      values = newObjectToMerge;\n      warn(\n        'Passing an Array to Firebase.update() is deprecated. ' +\n          'Use set() if you want to overwrite the existing data, or ' +\n          'an Object with integer keys if you really do want to ' +\n          'only update some of the children.'\n      );\n    }\n    _validateWritablePath('Reference.update', this._delegate._path);\n    validateCallback('Reference.update', 'onComplete', onComplete, true);\n\n    const result = update(this._delegate, values);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  setWithPriority(\n    newVal: unknown,\n    newPriority: string | number | null,\n    onComplete?: (a: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('Reference.setWithPriority', 2, 3, arguments.length);\n    validateCallback(\n      'Reference.setWithPriority',\n      'onComplete',\n      onComplete,\n      true\n    );\n\n    const result = setWithPriority(this._delegate, newVal, newPriority);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  remove(onComplete?: (a: Error | null) => void): Promise<void> {\n    validateArgCount('Reference.remove', 0, 1, arguments.length);\n    validateCallback('Reference.remove', 'onComplete', onComplete, true);\n\n    const result = remove(this._delegate);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  transaction(\n    transactionUpdate: (currentData: unknown) => unknown,\n    onComplete?: (\n      error: Error | null,\n      committed: boolean,\n      dataSnapshot: DataSnapshot | null\n    ) => void,\n    applyLocally?: boolean\n  ): Promise<TransactionResult> {\n    validateArgCount('Reference.transaction', 1, 3, arguments.length);\n    validateCallback(\n      'Reference.transaction',\n      'transactionUpdate',\n      transactionUpdate,\n      false\n    );\n    validateCallback('Reference.transaction', 'onComplete', onComplete, true);\n    validateBoolean(\n      'Reference.transaction',\n      'applyLocally',\n      applyLocally,\n      true\n    );\n\n    const result = runTransaction(this._delegate, transactionUpdate, {\n      applyLocally\n    }).then(\n      transactionResult =>\n        new TransactionResult(\n          transactionResult.committed,\n          new DataSnapshot(this.database, transactionResult.snapshot)\n        )\n    );\n    if (onComplete) {\n      result.then(\n        transactionResult =>\n          onComplete(\n            null,\n            transactionResult.committed,\n            transactionResult.snapshot\n          ),\n        error => onComplete(error, false, null)\n      );\n    }\n    return result;\n  }\n\n  setPriority(\n    priority: string | number | null,\n    onComplete?: (a: Error | null) => void\n  ): Promise<void> {\n    validateArgCount('Reference.setPriority', 1, 2, arguments.length);\n    validateCallback('Reference.setPriority', 'onComplete', onComplete, true);\n\n    const result = setPriority(this._delegate, priority);\n    if (onComplete) {\n      result.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n    return result;\n  }\n\n  push(value?: unknown, onComplete?: (a: Error | null) => void): Reference {\n    validateArgCount('Reference.push', 0, 2, arguments.length);\n    validateCallback('Reference.push', 'onComplete', onComplete, true);\n\n    const expPromise = push(this._delegate, value);\n    const promise = expPromise.then(\n      expRef => new Reference(this.database, expRef)\n    );\n\n    if (onComplete) {\n      promise.then(\n        () => onComplete(null),\n        error => onComplete(error)\n      );\n    }\n\n    const result = new Reference(this.database, expPromise);\n    result.then = promise.then.bind(promise);\n    result.catch = promise.catch.bind(promise, undefined);\n    return result;\n  }\n\n  onDisconnect(): OnDisconnect {\n    _validateWritablePath('Reference.onDisconnect', this._delegate._path);\n    return new OnDisconnect(\n      new ModularOnDisconnect(this._delegate._repo, this._delegate._path)\n    );\n  }\n\n  get key(): string | null {\n    return this.getKey();\n  }\n\n  get parent(): Reference | null {\n    return this.getParent();\n  }\n\n  get root(): Reference {\n    return this.getRoot();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\n\nimport { FirebaseApp } from '@firebase/app-types';\nimport { FirebaseService } from '@firebase/app-types/private';\nimport {\n  forceLongPolling,\n  forceWebSockets,\n  goOnline,\n  connectDatabaseEmulator,\n  goOffline,\n  ref,\n  refFromURL,\n  increment,\n  serverTimestamp,\n  Database as ModularDatabase\n} from '@firebase/database';\nimport {\n  validateArgCount,\n  Compat,\n  EmulatorMockTokenOptions\n} from '@firebase/util';\n\nimport { Reference } from './Reference';\n\n/**\n * Class representing a firebase database.\n */\nexport class Database implements FirebaseService, Compat<ModularDatabase> {\n  static readonly ServerValue = {\n    TIMESTAMP: serverTimestamp(),\n    increment: (delta: number) => increment(delta)\n  };\n\n  /**\n   * The constructor should not be called by users of our public API.\n   */\n  constructor(readonly _delegate: ModularDatabase, readonly app: FirebaseApp) {}\n\n  INTERNAL = {\n    delete: () => this._delegate._delete(),\n    forceWebSockets,\n    forceLongPolling\n  };\n\n  /**\n   * Modify this instance to communicate with the Realtime Database emulator.\n   *\n   * <p>Note: This method must be called before performing any other operation.\n   *\n   * @param host - the emulator host (ex: localhost)\n   * @param port - the emulator port (ex: 8080)\n   * @param options.mockUserToken - the mock auth token to use for unit testing Security Rules\n   */\n  useEmulator(\n    host: string,\n    port: number,\n    options: {\n      mockUserToken?: EmulatorMockTokenOptions;\n    } = {}\n  ): void {\n    connectDatabaseEmulator(this._delegate, host, port, options);\n  }\n\n  /**\n   * Returns a reference to the root or to the path specified in the provided\n   * argument.\n   *\n   * @param path - The relative string path or an existing Reference to a database\n   * location.\n   * @throws If a Reference is provided, throws if it does not belong to the\n   * same project.\n   * @returns Firebase reference.\n   */\n  ref(path?: string): Reference;\n  ref(path?: Reference): Reference;\n  ref(path?: string | Reference): Reference {\n    validateArgCount('database.ref', 0, 1, arguments.length);\n    if (path instanceof Reference) {\n      const childRef = refFromURL(this._delegate, path.toString());\n      return new Reference(this, childRef);\n    } else {\n      const childRef = ref(this._delegate, path);\n      return new Reference(this, childRef);\n    }\n  }\n\n  /**\n   * Returns a reference to the root or the path specified in url.\n   * We throw a exception if the url is not in the same domain as the\n   * current repo.\n   * @returns Firebase reference.\n   */\n  refFromURL(url: string): Reference {\n    const apiName = 'database.refFromURL';\n    validateArgCount(apiName, 1, 1, arguments.length);\n    const childRef = refFromURL(this._delegate, url);\n    return new Reference(this, childRef);\n  }\n\n  // Make individual repo go offline.\n  goOffline(): void {\n    validateArgCount('database.goOffline', 0, 0, arguments.length);\n    return goOffline(this._delegate);\n  }\n\n  goOnline(): void {\n    validateArgCount('database.goOnline', 0, 0, arguments.length);\n    return goOnline(this._delegate);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { FirebaseApp } from '@firebase/app-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  Provider\n} from '@firebase/component';\nimport {\n  _repoManagerDatabaseFromApp,\n  _setSDKVersion\n} from '@firebase/database';\nimport * as types from '@firebase/database-types';\n\nimport { Database } from './Database';\n\n/**\n * Used by console to create a database based on the app,\n * passed database URL and a custom auth implementation.\n *\n * @param app - A valid FirebaseApp-like object\n * @param url - A valid Firebase databaseURL\n * @param version - custom version e.g. firebase-admin version\n * @param customAuthImpl - custom auth implementation\n */\nexport function initStandalone<T>({\n  app,\n  url,\n  version,\n  customAuthImpl,\n  customAppCheckImpl,\n  namespace,\n  nodeAdmin = false\n}: {\n  app: FirebaseApp;\n  url: string;\n  version: string;\n  customAuthImpl: FirebaseAuthInternal;\n  customAppCheckImpl?: FirebaseAppCheckInternal;\n  namespace: T;\n  nodeAdmin?: boolean;\n}): {\n  instance: types.Database;\n  namespace: T;\n} {\n  _setSDKVersion(version);\n\n  const container = new ComponentContainer('database-standalone');\n  /**\n   * ComponentContainer('database-standalone') is just a placeholder that doesn't perform\n   * any actual function.\n   */\n  const authProvider = new Provider<FirebaseAuthInternalName>(\n    'auth-internal',\n    container\n  );\n  authProvider.setComponent(\n    new Component('auth-internal', () => customAuthImpl, ComponentType.PRIVATE)\n  );\n\n  let appCheckProvider: Provider<AppCheckInternalComponentName> = undefined;\n  if (customAppCheckImpl) {\n    appCheckProvider = new Provider<AppCheckInternalComponentName>(\n      'app-check-internal',\n      container\n    );\n    appCheckProvider.setComponent(\n      new Component(\n        'app-check-internal',\n        () => customAppCheckImpl,\n        ComponentType.PRIVATE\n      )\n    );\n  }\n\n  return {\n    instance: new Database(\n      _repoManagerDatabaseFromApp(\n        app,\n        authProvider,\n        appCheckProvider,\n        url,\n        nodeAdmin\n      ),\n      app\n    ) as types.Database,\n    namespace\n  };\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport firebase, { FirebaseNamespace } from '@firebase/app-compat';\nimport { _FirebaseNamespace } from '@firebase/app-types/private';\nimport { Component, ComponentType } from '@firebase/component';\nimport { enableLogging } from '@firebase/database';\nimport * as types from '@firebase/database-types';\n\nimport { name, version } from '../package.json';\nimport { Database } from '../src/api/Database';\nimport * as INTERNAL from '../src/api/internal';\nimport { DataSnapshot, Query, Reference } from '../src/api/Reference';\n\nconst ServerValue = Database.ServerValue;\n\nexport function registerDatabase(instance: FirebaseNamespace) {\n  // Register the Database Service with the 'firebase' namespace.\n  (instance as unknown as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component(\n      'database-compat',\n      (container, { instanceIdentifier: url }) => {\n        /* Dependencies */\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app-compat').getImmediate();\n        const databaseExp = container\n          .getProvider('database')\n          .getImmediate({ identifier: url });\n        return new Database(databaseExp, app);\n      },\n      ComponentType.PUBLIC\n    )\n      .setServiceProps(\n        // firebase.database namespace properties\n        {\n          Reference,\n          Query,\n          Database,\n          DataSnapshot,\n          enableLogging,\n          INTERNAL,\n          ServerValue\n        }\n      )\n      .setMultipleInstances(true)\n  );\n\n  instance.registerVersion(name, version);\n}\n\nregisterDatabase(firebase);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    database?: {\n      (app?: FirebaseApp): types.FirebaseDatabase;\n      enableLogging: typeof types.enableLogging;\n      ServerValue: types.ServerValue;\n      Database: typeof types.FirebaseDatabase;\n    };\n  }\n  interface FirebaseApp {\n    database?(databaseURL?: string): types.FirebaseDatabase;\n  }\n}\n"], "names": ["errorPrefixFxn", "ModularOnDisconnect"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAIH,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAEnD,MAAM,IAAI,GAAG,UAAU,GAAW,EAAA;AACvC,IAAA,MAAM,OAAO,GAAG,oBAAoB,GAAG,GAAG,CAAC;AAC3C,IAAA,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1B,CAAC;;ACxBD;;;;;;;;;;;;;;;AAeG;AAII,MAAM,eAAe,GAAG,UAC7B,MAAc,EACd,YAAoB,EACpB,IAAa,EACb,QAAiB,EAAA;AAEjB,IAAA,IAAI,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE;QAClC,OAAO;AACR,KAAA;AACD,IAAA,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;AAC7B,QAAA,MAAM,IAAI,KAAK,CACbA,WAAc,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,oBAAoB,CAC5D,CAAC;AACH,KAAA;AACH,CAAC,CAAC;AAEK,MAAM,iBAAiB,GAAG,UAC/B,MAAc,EACd,SAAiB,EACjB,QAAiB,EAAA;AAEjB,IAAA,IAAI,QAAQ,IAAI,SAAS,KAAK,SAAS,EAAE;QACvC,OAAO;AACR,KAAA;AAED,IAAA,QAAQ,SAAS;AACf,QAAA,KAAK,OAAO,CAAC;AACb,QAAA,KAAK,aAAa,CAAC;AACnB,QAAA,KAAK,eAAe,CAAC;AACrB,QAAA,KAAK,eAAe,CAAC;AACrB,QAAA,KAAK,aAAa;YAChB,MAAM;AACR,QAAA;YACE,MAAM,IAAI,KAAK,CACbA,WAAc,CAAC,MAAM,EAAE,WAAW,CAAC;gBACjC,wEAAwE;AACxE,gBAAA,oCAAoC,CACvC,CAAC;AACL,KAAA;AACH,CAAC;;AC1DD;;;;;;;;;;;;;;;AAeG;MAMU,YAAY,CAAA;AACvB,IAAA,WAAA,CAAqB,SAA8B,EAAA;QAA9B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAqB;KAAI;AAEvD,IAAA,MAAM,CAAC,UAAsC,EAAA;QAC3C,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAChE,gBAAgB,CAAC,qBAAqB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AACvC,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,MAAM,CAAC,UAAsC,EAAA;QAC3C,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAChE,gBAAgB,CAAC,qBAAqB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AACvC,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;IAED,GAAG,CAAC,KAAc,EAAE,UAAsC,EAAA;QACxD,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,gBAAgB,CAAC,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACzC,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,eAAe,CACb,KAAc,EACd,QAAgC,EAChC,UAAsC,EAAA;QAEtC,gBAAgB,CAAC,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACzE,gBAAgB,CACd,8BAA8B,EAC9B,YAAY,EACZ,UAAU,EACV,IAAI,CACL,CAAC;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC/D,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;IAED,MAAM,CACJ,aAAsC,EACtC,UAAsC,EAAA;QAEtC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAChE,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YAChC,MAAM,gBAAgB,GAA6B,EAAE,CAAC;AACtD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC7C,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AAC7C,aAAA;YACD,aAAa,GAAG,gBAAgB,CAAC;AACjC,YAAA,IAAI,CACF,sHAAsH;AACpH,gBAAA,0GAA0G,CAC7G,CAAC;AACH,SAAA;QACD,gBAAgB,CAAC,qBAAqB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACpD,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACF;;AC/GD;;;;;;;;;;;;;;;AAeG;MAMU,iBAAiB,CAAA;AAC5B;;AAEG;IACH,WAAmB,CAAA,SAAkB,EAAS,QAAsB,EAAA;QAAjD,IAAS,CAAA,SAAA,GAAT,SAAS,CAAS;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAc;KAAI;;;IAIxE,MAAM,GAAA;QACJ,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;KACxE;AACF;;ACjCD;;;;;;;;;;;;;;;AAeG;AA0DH;;;AAGG;MACU,YAAY,CAAA;IACvB,WACW,CAAA,SAAmB,EACnB,SAA8B,EAAA;QAD9B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAqB;KACrC;AAEJ;;;;;AAKG;IACH,GAAG,GAAA;QACD,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7D,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;KAC7B;AAED;;;;AAIG;IACH,SAAS,GAAA;QACP,gBAAgB,CAAC,wBAAwB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACnE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;KACnC;;;IAID,MAAM,GAAA;;QAEJ,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAChE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;AAED;;;;AAIG;IACH,MAAM,GAAA;QACJ,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAChE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;AAED;;;;;AAKG;AACH,IAAA,KAAK,CAAC,IAAY,EAAA;QAChB,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;;AAE/D,QAAA,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACpB,mBAAmB,CAAC,oBAAoB,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/D,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KACrE;AAED;;;;;AAKG;AACH,IAAA,QAAQ,CAAC,IAAY,EAAA;QACnB,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClE,mBAAmB,CAAC,uBAAuB,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACtC;AAED;;;;AAIG;IACH,WAAW,GAAA;QACT,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AAED;;;;;;;AAOG;AACH,IAAA,OAAO,CAAC,MAA0D,EAAA;QAChE,gBAAgB,CAAC,sBAAsB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACjE,gBAAgB,CAAC,sBAAsB,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,IAC3C,MAAM,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,CAC1D,CAAC;KACH;AAED;;;AAGG;IACH,WAAW,GAAA;QACT,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;KACrC;AAED,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;KAC3B;AAED;;;AAGG;IACH,WAAW,GAAA;QACT,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AAED;;;AAGG;IACH,MAAM,GAAA;QACJ,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7D,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KAC1D;AAED,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;KACtB;AACF,CAAA;AAaD;;;;;AAKG;MACU,KAAK,CAAA;IAChB,WAAqB,CAAA,QAAkB,EAAW,SAAmB,EAAA;QAAhD,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAW,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;KAAI;AAEzE,IAAA,EAAE,CACA,SAAiB,EACjB,QAA0B,EAC1B,uBAAiE,EACjE,OAAuB,EAAA;;QAEvB,gBAAgB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrD,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAE1D,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,wBAAwB,CACxC,UAAU,EACV,uBAAuB,EACvB,OAAO,CACR,CAAC;AACF,QAAA,MAAM,aAAa,GAAG,CAAC,WAAW,EAAE,iBAAkB,KAAI;AACxD,YAAA,QAAQ,CAAC,IAAI,CACX,GAAG,CAAC,OAAO,EACX,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,EAC5C,iBAAiB,CAClB,CAAC;AACJ,SAAC,CAAC;AACF,QAAA,aAAa,CAAC,YAAY,GAAG,QAAQ,CAAC;AACtC,QAAA,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AACpC,QAAA,MAAM,cAAc,GAAG,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAErD,QAAA,QAAQ,SAAS;AACf,YAAA,KAAK,OAAO;gBACV,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AACvD,gBAAA,OAAO,QAAQ,CAAC;AAClB,YAAA,KAAK,aAAa;gBAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC5D,gBAAA,OAAO,QAAQ,CAAC;AAClB,YAAA,KAAK,eAAe;gBAClB,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC9D,gBAAA,OAAO,QAAQ,CAAC;AAClB,YAAA,KAAK,eAAe;gBAClB,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC9D,gBAAA,OAAO,QAAQ,CAAC;AAClB,YAAA,KAAK,aAAa;gBAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC5D,gBAAA,OAAO,QAAQ,CAAC;AAClB,YAAA;gBACE,MAAM,IAAI,KAAK,CACb,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC;oBAClC,wEAAwE;AACxE,oBAAA,oCAAoC,CACvC,CAAC;AACL,SAAA;KACF;AAED,IAAA,GAAG,CACD,SAAkB,EAClB,QAA2B,EAC3B,OAAuB,EAAA;QAEvB,gBAAgB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACtD,QAAA,iBAAiB,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAChD,gBAAgB,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1D,qBAAqB,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7D,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,aAAa,GAAkB,MAAK,GAAG,CAAC;AAC9C,YAAA,aAAa,CAAC,YAAY,GAAG,QAAQ,CAAC;AACtC,YAAA,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;YAChC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,SAAsB,EAAE,aAAa,CAAC,CAAC;AAC5D,SAAA;AAAM,aAAA;AACL,YAAA,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,SAAkC,CAAC,CAAC;AACzD,SAAA;KACF;AAED;;AAEG;IACH,GAAG,GAAA;QACD,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,IAAG;YAC5C,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AACtD,SAAC,CAAC,CAAC;KACJ;AAED;;AAEG;AACH,IAAA,IAAI,CACF,SAAiB,EACjB,QAA2B,EAC3B,wBAA+D,EAC/D,OAAuB,EAAA;QAEvB,gBAAgB,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACvD,gBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAE3D,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,wBAAwB,CACxC,YAAY,EACZ,wBAAwB,EACxB,OAAO,CACR,CAAC;AACF,QAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAgB,CAAC;AAC9C,QAAA,MAAM,aAAa,GAAkB,CAAC,WAAW,EAAE,iBAAkB,KAAI;YACvE,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC5D,YAAA,IAAI,QAAQ,EAAE;gBACZ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;AACvD,aAAA;AACD,YAAA,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3B,SAAC,CAAC;AACF,QAAA,aAAa,CAAC,YAAY,GAAG,QAAQ,CAAC;AACtC,QAAA,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AACpC,QAAA,MAAM,cAAc,GAAG,CAAC,KAAY,KAAI;YACtC,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACrC,aAAA;AACD,YAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzB,SAAC,CAAC;AAEF,QAAA,QAAQ,SAAS;AACf,YAAA,KAAK,OAAO;gBACV,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;AACrD,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA,CAAC,CAAC;gBACH,MAAM;AACR,YAAA,KAAK,aAAa;gBAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;AAC1D,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA,CAAC,CAAC;gBACH,MAAM;AACR,YAAA,KAAK,eAAe;gBAClB,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;AAC5D,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA,CAAC,CAAC;gBACH,MAAM;AACR,YAAA,KAAK,eAAe;gBAClB,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;AAC5D,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA,CAAC,CAAC;gBACH,MAAM;AACR,YAAA,KAAK,aAAa;gBAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE;AAC1D,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA,CAAC,CAAC;gBACH,MAAM;AACR,YAAA;gBACE,MAAM,IAAI,KAAK,CACb,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC;oBACpC,wEAAwE;AACxE,oBAAA,oCAAoC,CACvC,CAAC;AACL,SAAA;QAED,OAAO,QAAQ,CAAC,OAAO,CAAC;KACzB;AAED;;AAEG;AACH,IAAA,YAAY,CAAC,KAAa,EAAA;QACxB,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/D,QAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC7E;AAED;;AAEG;AACH,IAAA,WAAW,CAAC,KAAa,EAAA;QACvB,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC9D,QAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC5E;AAED;;AAEG;AACH,IAAA,YAAY,CAAC,IAAY,EAAA;QACvB,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/D,QAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAC5E;AAED;;AAEG;IACH,UAAU,GAAA;QACR,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7D,QAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;KACtE;AAED;;AAEG;IACH,eAAe,GAAA;QACb,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAClE,QAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;KAC3E;AAED;;AAEG;IACH,YAAY,GAAA;QACV,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/D,QAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;KACxE;AAED,IAAA,OAAO,CACL,KAAA,GAA0C,IAAI,EAC9C,IAAoB,EAAA;QAEpB,gBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACb,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC5C,CAAC;KACH;AAED,IAAA,UAAU,CACR,KAAA,GAA0C,IAAI,EAC9C,IAAoB,EAAA;QAEpB,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACb,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC/C,CAAC;KACH;AAED,IAAA,KAAK,CACH,KAAA,GAA0C,IAAI,EAC9C,IAAoB,EAAA;QAEpB,gBAAgB,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACxD,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;KAC5E;AAED,IAAA,SAAS,CACP,KAAA,GAA0C,IAAI,EAC9C,IAAoB,EAAA;QAEpB,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACb,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC9C,CAAC;KACH;AAED;;;AAGG;IACH,OAAO,CAAC,KAAuC,EAAE,IAAa,EAAA;QAC5D,gBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,QAAQ,EACb,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAC5C,CAAC;KACH;AAED;;AAEG;IACH,QAAQ,GAAA;QACN,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3D,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAClC;;;IAID,MAAM,GAAA;;QAEJ,gBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACzD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,KAAY,EAAA;QAClB,gBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC1D,QAAA,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE;YAC7B,MAAM,KAAK,GACT,sFAAsF,CAAC;AACzF,YAAA,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;AACxB,SAAA;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KAChD;AAED;;;;AAIG;AACK,IAAA,OAAO,wBAAwB,CACrC,MAAc,EACd,eAAsD,EACtD,OAAuB,EAAA;QAEvB,MAAM,GAAG,GAGL,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAC9C,IAAI,eAAe,IAAI,OAAO,EAAE;AAC9B,YAAA,GAAG,CAAC,MAAM,GAAG,eAAqC,CAAC;YACnD,gBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAErD,YAAA,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;YACtB,qBAAqB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA,IAAI,eAAe,EAAE;;YAE1B,IAAI,OAAO,eAAe,KAAK,QAAQ,IAAI,eAAe,KAAK,IAAI,EAAE;;AAEnE,gBAAA,GAAG,CAAC,OAAO,GAAG,eAAe,CAAC;AAC/B,aAAA;AAAM,iBAAA,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AAChD,gBAAA,GAAG,CAAC,MAAM,GAAG,eAAqC,CAAC;AACpD,aAAA;AAAM,iBAAA;gBACL,MAAM,IAAI,KAAK,CACb,WAAW,CAAC,MAAM,EAAE,iBAAiB,CAAC;AACpC,oBAAA,wDAAwD,CAC3D,CAAC;AACH,aAAA;AACF,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACZ;AAED,IAAA,IAAI,GAAG,GAAA;QACL,OAAO,IAAI,SAAS,CAClB,IAAI,CAAC,QAAQ,EACb,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAC/D,CAAC;KACH;AACF,CAAA;AAEK,MAAO,SAAU,SAAQ,KAAK,CAAA;AAIlC;;;;;;AAMG;IACH,WACW,CAAA,QAAkB,EAClB,SAA2B,EAAA;QAEpC,KAAK,CACH,QAAQ,EACR,IAAI,UAAU,CACZ,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,KAAK,EACf,IAAI,YAAY,EAAE,EAClB,KAAK,CACN,CACF,CAAC;QAXO,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAClB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkB;KAWrC;;IAGD,MAAM,GAAA;QACJ,gBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC1D,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;KAC3B;AAED,IAAA,KAAK,CAAC,UAAkB,EAAA;QACtB,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC5D,QAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAClC,YAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;KACxE;;IAGD,SAAS,GAAA;QACP,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7D,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACrC,QAAA,OAAO,MAAM,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;KAC7D;;IAGD,OAAO,GAAA;QACL,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3D,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC1D;IAED,GAAG,CACD,MAAe,EACf,UAA0C,EAAA;QAE1C,gBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,gBAAgB,CAAC,eAAe,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAClE,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;IAED,MAAM,CACJ,MAAc,EACd,UAAsC,EAAA;QAEtC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAE7D,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,gBAAgB,GAA6B,EAAE,CAAC;AACtD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACtC,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,aAAA;YACD,MAAM,GAAG,gBAAgB,CAAC;AAC1B,YAAA,IAAI,CACF,uDAAuD;gBACrD,2DAA2D;gBAC3D,uDAAuD;AACvD,gBAAA,mCAAmC,CACtC,CAAC;AACH,SAAA;QACD,qBAAqB,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAChE,gBAAgB,CAAC,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAErE,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC9C,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,eAAe,CACb,MAAe,EACf,WAAmC,EACnC,UAAsC,EAAA;QAEtC,gBAAgB,CAAC,2BAA2B,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACtE,gBAAgB,CACd,2BAA2B,EAC3B,YAAY,EACZ,UAAU,EACV,IAAI,CACL,CAAC;AAEF,QAAA,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AACpE,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,MAAM,CAAC,UAAsC,EAAA;QAC3C,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,gBAAgB,CAAC,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAErE,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtC,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,WAAW,CACT,iBAAoD,EACpD,UAIS,EACT,YAAsB,EAAA;QAEtB,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClE,gBAAgB,CACd,uBAAuB,EACvB,mBAAmB,EACnB,iBAAiB,EACjB,KAAK,CACN,CAAC;QACF,gBAAgB,CAAC,uBAAuB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1E,eAAe,CACb,uBAAuB,EACvB,cAAc,EACd,YAAY,EACZ,IAAI,CACL,CAAC;QAEF,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,iBAAiB,EAAE;YAC/D,YAAY;SACb,CAAC,CAAC,IAAI,CACL,iBAAiB,IACf,IAAI,iBAAiB,CACnB,iBAAiB,CAAC,SAAS,EAC3B,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAC5D,CACJ,CAAC;AACF,QAAA,IAAI,UAAU,EAAE;AACd,YAAA,MAAM,CAAC,IAAI,CACT,iBAAiB,IACf,UAAU,CACR,IAAI,EACJ,iBAAiB,CAAC,SAAS,EAC3B,iBAAiB,CAAC,QAAQ,CAC3B,EACH,KAAK,IAAI,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CACxC,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;IAED,WAAW,CACT,QAAgC,EAChC,UAAsC,EAAA;QAEtC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClE,gBAAgB,CAAC,uBAAuB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAE1E,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrD,QAAA,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,IAAI,CACT,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;IAED,IAAI,CAAC,KAAe,EAAE,UAAsC,EAAA;QAC1D,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3D,gBAAgB,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAEnE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAC7B,MAAM,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAC/C,CAAC;AAEF,QAAA,IAAI,UAAU,EAAE;YACd,OAAO,CAAC,IAAI,CACV,MAAM,UAAU,CAAC,IAAI,CAAC,EACtB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAC3B,CAAC;AACH,SAAA;QAED,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACxD,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,QAAA,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACtD,QAAA,OAAO,MAAM,CAAC;KACf;IAED,YAAY,GAAA;QACV,qBAAqB,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACtE,QAAA,OAAO,IAAI,YAAY,CACrB,IAAIC,cAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CACpE,CAAC;KACH;AAED,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;KACtB;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;KACzB;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;AACF;;AC5xBD;;;;;;;;;;;;;;;AAeG;AAyBH;;AAEG;MACU,QAAQ,CAAA;AAMnB;;AAEG;IACH,WAAqB,CAAA,SAA0B,EAAW,GAAgB,EAAA;QAArD,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;QAAW,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;AAE1E,QAAA,IAAA,CAAA,QAAQ,GAAG;YACT,MAAM,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACtC,eAAe;YACf,gBAAgB;SACjB,CAAC;KAN4E;AAQ9E;;;;;;;;AAQG;AACH,IAAA,WAAW,CACT,IAAY,EACZ,IAAY,EACZ,UAEI,EAAE,EAAA;QAEN,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KAC9D;AAcD,IAAA,GAAG,CAAC,IAAyB,EAAA;QAC3B,gBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,IAAI,YAAY,SAAS,EAAE;AAC7B,YAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC7D,YAAA,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACtC,SAAA;AAAM,aAAA;YACL,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC3C,YAAA,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACtC,SAAA;KACF;AAED;;;;;AAKG;AACH,IAAA,UAAU,CAAC,GAAW,EAAA;QACpB,MAAM,OAAO,GAAG,qBAAqB,CAAC;QACtC,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AACjD,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACtC;;IAGD,SAAS,GAAA;QACP,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/D,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAClC;IAED,QAAQ,GAAA;QACN,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAC9D,QAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACjC;;AAhFe,QAAA,CAAA,WAAW,GAAG;IAC5B,SAAS,EAAE,eAAe,EAAE;IAC5B,SAAS,EAAE,CAAC,KAAa,KAAK,SAAS,CAAC,KAAK,CAAC;CAC/C;;ACRH;;;;;;;;AAQG;SACa,cAAc,CAAI,EAChC,GAAG,EACH,GAAG,EACH,OAAO,EACP,cAAc,EACd,kBAAkB,EAClB,SAAS,EACT,SAAS,GAAG,KAAK,EASlB,EAAA;IAIC,cAAc,CAAC,OAAO,CAAC,CAAC;AAExB,IAAA,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;AAChE;;;AAGG;IACH,MAAM,YAAY,GAAG,IAAI,QAAQ,CAC/B,eAAe,EACf,SAAS,CACV,CAAC;AACF,IAAA,YAAY,CAAC,YAAY,CACvB,IAAI,SAAS,CAAC,eAAe,EAAE,MAAM,cAAc,EAAA,SAAA,6BAAwB,CAC5E,CAAC;IAEF,IAAI,gBAAgB,GAA4C,SAAS,CAAC;AAC1E,IAAA,IAAI,kBAAkB,EAAE;QACtB,gBAAgB,GAAG,IAAI,QAAQ,CAC7B,oBAAoB,EACpB,SAAS,CACV,CAAC;AACF,QAAA,gBAAgB,CAAC,YAAY,CAC3B,IAAI,SAAS,CACX,oBAAoB,EACpB,MAAM,kBAAkB,EAAA,SAAA,6BAEzB,CACF,CAAC;AACH,KAAA;IAED,OAAO;AACL,QAAA,QAAQ,EAAE,IAAI,QAAQ,CACpB,2BAA2B,CACzB,GAAG,EACH,YAAY,EACZ,gBAAgB,EAChB,GAAG,EACH,SAAS,CACV,EACD,GAAG,CACc;QACnB,SAAS;KACV,CAAC;AACJ;;;;;;;AC/GA;;;;;;;;;;;;;;;AAeG;AAcH,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;AAEnC,SAAU,gBAAgB,CAAC,QAA2B,EAAA;;AAEzD,IAAA,QAA0C,CAAC,QAAQ,CAAC,iBAAiB,CACpE,IAAI,SAAS,CACX,iBAAiB,EACjB,CAAC,SAAS,EAAE,EAAE,kBAAkB,EAAE,GAAG,EAAE,KAAI;;;QAGzC,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;QAC/D,MAAM,WAAW,GAAG,SAAS;aAC1B,WAAW,CAAC,UAAU,CAAC;AACvB,aAAA,YAAY,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;AACrC,QAAA,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACxC,KAAC,EAEF,QAAA,4BAAA;SACE,eAAe;;AAEd,IAAA;QACE,SAAS;QACT,KAAK;QACL,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,QAAQ;QACR,WAAW;KACZ,CACF;AACA,SAAA,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAC;AAEF,IAAA,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED,gBAAgB,CAAC,QAAQ,CAAC;;;;"}