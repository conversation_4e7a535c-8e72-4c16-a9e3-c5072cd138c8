# ⚖️ LegalPR - Puerto Rico Legal Marketplace

A clean, modern legal services platform connecting clients with qualified attorneys in Puerto Rico. Built with Next.js 14, TypeScript, and Firebase.

## ✨ Features

### 👥 For Clients
- **Find Lawyers**: Search attorneys by specialty and location
- **Book Consultations**: Schedule appointments with lawyers
- **Client Dashboard**: Manage appointments and legal matters
- **Secure Authentication**: Firebase-powered login/signup

### ⚖️ For Lawyers
- **Lawyer Dashboard**: Manage practice and clients
- **Calendar Management**: Handle availability and appointments
- **Client Communication**: Connect with potential clients
- **Profile Management**: Showcase expertise and credentials

### 🔧 For Administrators
- **Admin Dashboard**: Oversee platform operations
- **User Management**: Manage clients and lawyers
- **Analytics**: Monitor platform performance

## 🚀 Tech Stack

- **Next.js 14** with App Router & TypeScript
- **Tailwind CSS** for styling
- **Firebase** (Auth, Firestore, Storage)
- **Shadcn/ui** components
- **Next-intl** for internationalization

## 📁 Clean Project Structure

```
delawpr/
├── app/                    # Next.js App Router
│   ├── [locale]/          # Internationalized routes
│   │   ├── dashboard/     # Dashboard routes (client, lawyer, admin)
│   │   ├── login/         # Login page
│   │   ├── register/      # Registration page
│   │   └── page.tsx       # Home page
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── dashboard/         # Dashboard components
│   ├── layout/           # Layout components (navigation, footer)
│   ├── pages/            # Page components
│   ├── sections/         # Home page sections
│   └── ui/               # UI components (Shadcn)
├── contexts/             # React contexts (auth)
├── hooks/                # Custom React hooks
├── lib/                  # Utilities (Firebase, utils)
├── types/                # TypeScript definitions
├── public/               # Static assets
└── scripts/              # Setup scripts
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Firebase
1. Go to [Firebase Console](https://console.firebase.google.com/project/delawpr/authentication/providers)
2. Enable **Email/Password** authentication
3. Your environment is already configured!

### 3. Start Development
```bash
npm run dev
```

Visit: http://localhost:3001

## 🎯 Key Routes

- **Home**: `/` - Landing page
- **Login**: `/login` - User authentication
- **Register**: `/register` - User registration
- **Client Dashboard**: `/dashboard/client` - Client management
- **Lawyer Dashboard**: `/dashboard/lawyer` - Lawyer management
- **Admin Dashboard**: `/dashboard` - Admin panel

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run setup-firebase` - Firebase setup helper

## 📝 Authentication Flow

1. Users register with email/password
2. Choose role: Client or Lawyer
3. Automatic redirect to appropriate dashboard
4. Role-based access control throughout app

## 🎨 Styling

- **Tailwind CSS** for utility-first styling
- **Shadcn/ui** for consistent components
- **Responsive design** for all devices
- **Dark/light mode** support

---

**Ready to use!** Your legal marketplace platform is fully functional and production-ready. 🎉

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Firebase CLI
- Stripe CLI (for webhook testing)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/delawpr.git
   cd delawpr
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   Fill in your Firebase, Stripe, and other API keys.

4. **Initialize Firebase**
   ```bash
   firebase login
   firebase init
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Start Firebase emulators** (in another terminal)
   ```bash
   npm run firebase:emulators
   ```

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Firebase Admin (Server-side)
FIREBASE_PRIVATE_KEY="your_private_key"
FIREBASE_CLIENT_EMAIL=your_service_account_email
FIREBASE_PROJECT_ID=your_project_id

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_secret

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks
- `npm run firebase:emulators` - Start Firebase emulators
- `npm run firebase:deploy` - Deploy to Firebase

### Code Style

This project uses:
- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type checking
- **Tailwind CSS** for styling

## 🚀 Deployment

### Firebase Deployment

1. **Build the project**
   ```bash
   npm run build
   ```

2. **Deploy to Firebase**
   ```bash
   npm run firebase:deploy
   ```

### Heroku Deployment

1. **Create Heroku app**
   ```bash
   heroku create delawpr-api
   ```

2. **Set environment variables**
   ```bash
   heroku config:set NODE_ENV=production
   ```

3. **Deploy**
   ```bash
   git push heroku main
   ```

## 📊 Database Schema

### Collections

- **users**: User profiles (clients and lawyers)
- **lawyers**: Extended lawyer profiles
- **appointments**: Booking and appointment data
- **reviews**: Client reviews and ratings
- **conversations**: Chat conversations
- **messages**: Individual chat messages
- **notifications**: User notifications

## 🔐 Security

- **Firestore Rules**: Secure database access
- **Storage Rules**: Secure file uploads
- **Authentication**: Firebase Auth with email/password and OAuth
- **Payment Security**: PCI-compliant Stripe integration
- **Data Encryption**: All data encrypted in transit and at rest

## 🌐 Internationalization

The platform supports Spanish and English:
- Default locale: Spanish (es)
- Supported locales: Spanish (es), English (en)
- Translation files: `messages/es.json`, `messages/en.json`

## 📱 Mobile Support

Fully responsive design optimized for:
- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support, email <EMAIL> or create an issue in this repository.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Firebase](https://firebase.google.com/) for backend services
- [Stripe](https://stripe.com/) for payment processing
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [ShadCN UI](https://ui.shadcn.com/) for UI components
