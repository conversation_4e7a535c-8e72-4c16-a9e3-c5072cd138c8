#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 Firebase Setup Helper\n');

// Check if Firebase CLI is installed
function checkFirebaseCLI() {
  try {
    const version = execSync('firebase --version', { encoding: 'utf8' });
    console.log('✅ Firebase CLI is installed:', version.trim());
    return true;
  } catch (error) {
    console.log('❌ Firebase CLI is not installed');
    console.log('📦 Install it with: npm install -g firebase-tools');
    return false;
  }
}

// Check if user is logged in to Firebase
function checkFirebaseLogin() {
  try {
    const result = execSync('firebase projects:list', { encoding: 'utf8' });
    console.log('✅ You are logged in to Firebase');
    return true;
  } catch (error) {
    console.log('❌ You are not logged in to Firebase');
    console.log('🔑 Login with: firebase login');
    return false;
  }
}

// Check environment file
function checkEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const hasRealConfig = !envContent.includes('your_firebase_api_key_here');
    
    if (hasRealConfig) {
      console.log('✅ Environment file has Firebase configuration');
      return true;
    } else {
      console.log('⚠️  Environment file exists but needs Firebase configuration');
      console.log('📝 Update .env.local with your Firebase project credentials');
      return false;
    }
  } else {
    console.log('❌ No .env.local file found');
    return false;
  }
}

// Main setup check
function main() {
  console.log('Checking Firebase setup...\n');
  
  const cliInstalled = checkFirebaseCLI();
  const loggedIn = cliInstalled ? checkFirebaseLogin() : false;
  const envConfigured = checkEnvFile();
  
  console.log('\n📋 Setup Status:');
  console.log(`Firebase CLI: ${cliInstalled ? '✅' : '❌'}`);
  console.log(`Firebase Login: ${loggedIn ? '✅' : '❌'}`);
  console.log(`Environment Config: ${envConfigured ? '✅' : '❌'}`);
  
  if (cliInstalled && loggedIn && envConfigured) {
    console.log('\n🎉 Firebase is ready to use!');
  } else {
    console.log('\n📖 Please follow the FIREBASE_SETUP_GUIDE.md for complete setup instructions');
  }
}

main();
