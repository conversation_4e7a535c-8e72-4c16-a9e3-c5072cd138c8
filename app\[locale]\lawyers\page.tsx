import { Metadata } from 'next';
import { setRequestLocale } from 'next-intl/server';
import { LawyersBrowse } from '@/components/lawyers/lawyers-browse';

export const metadata: Metadata = {
  title: 'Buscar Abogados - Abogo',
  description: 'Encuentra abogados especializados en Puerto Rico por categoría y especialidad',
};

interface LawyersBrowsePageProps {
  params: { locale: string };
}

export default function LawyersBrowsePage({ params: { locale } }: LawyersBrowsePageProps) {
  setRequestLocale(locale);
  
  return <LawyersBrowse />;
}
