'use client';

import React from 'react';
import { PaymentForm } from './payment-form';
import { X } from 'lucide-react';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  lawyerId: string;
  lawyerName: string;
  appointmentId?: string;
  appointmentDate?: string;
  appointmentTime?: string;
  specialty?: string;
  description: string;
  onSuccess?: (paymentId: string) => void;
  onError?: (error: string) => void;
}

export function PaymentModal({
  isOpen,
  onClose,
  amount,
  lawyerId,
  lawyerName,
  appointmentId,
  appointmentDate,
  appointmentTime,
  specialty,
  description,
  onSuccess,
  onError
}: PaymentModalProps) {
  if (!isOpen) return null;

  const handleSuccess = (paymentId: string) => {
    onSuccess?.(paymentId);
    // Don't auto-close, let the success screen handle navigation
  };

  const handleError = (error: string) => {
    onError?.(error);
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
      {/* Background overlay */}
      <div 
        className="absolute inset-0" 
        onClick={onClose}
      />
      
      {/* Modal content */}
      <div className="relative w-full max-w-lg mx-auto my-8">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute -top-4 -right-4 z-10 w-10 h-10 bg-black/80 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 rounded-full flex items-center justify-center transition-all duration-200"
        >
          <X className="h-5 w-5" />
        </button>

        {/* Payment form */}
        <PaymentForm
          amount={amount}
          lawyerId={lawyerId}
          lawyerName={lawyerName}
          appointmentId={appointmentId}
          appointmentDate={appointmentDate}
          appointmentTime={appointmentTime}
          specialty={specialty}
          description={description}
          onSuccess={handleSuccess}
          onError={handleError}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
}
