'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  MessageSquare,
  Search,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  User,
  LogOut,
  Scale,
  MapPin,
  Phone,
  Mail,
  Briefcase
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function ClientDashboard() {
  const { user, logout } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [messages, setMessages] = useState([]);
  const [lawyers, setLawyers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load user's appointments
      const appointmentsQuery = query(
        collection(db, 'appointments'),
        where('clientId', '==', user.id),
        orderBy('date', 'desc'),
        limit(5)
      );
      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      const appointmentsData = appointmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setAppointments(appointmentsData);

      // Load recent messages
      const messagesQuery = query(
        collection(db, 'messages'),
        where('clientId', '==', user.id),
        orderBy('timestamp', 'desc'),
        limit(5)
      );
      const messagesSnapshot = await getDocs(messagesQuery);
      const messagesData = messagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setMessages(messagesData);

      // Load featured lawyers
      const lawyersQuery = query(
        collection(db, 'lawyers'),
        where('isActive', '==', true),
        orderBy('rating', 'desc'),
        limit(6)
      );
      const lawyersSnapshot = await getDocs(lawyersQuery);
      const lawyersData = lawyersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setLawyers(lawyersData);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
      </div>

      <div className="relative z-10 p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">
              ¡Bienvenido/a, {user?.firstName}!
            </h1>
            <p className="mt-2 text-gray-400">
              Gestiona tus citas legales y encuentra los mejores abogados en Puerto Rico.
            </p>
          </div>
          <Button
            onClick={handleLogout}
            variant="outline"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Cerrar Sesión
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {/* Buscar Abogados */}
          <Card className="bg-white/5 border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white">Buscar Abogados</CardTitle>
              <Search className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{lawyers.length}+</div>
              <p className="text-xs text-gray-400">
                Abogados disponibles
              </p>
              <Button className="w-full mt-4 bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 hover:from-yellow-500 hover:via-orange-600 hover:to-yellow-500 text-black font-semibold">
                <Search className="mr-2 h-4 w-4" />
                Buscar Ahora
              </Button>
            </CardContent>
          </Card>

          {/* Citas */}
          <Card className="bg-white/5 border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white">Mis Citas</CardTitle>
              <Calendar className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{appointments.length}</div>
              <p className="text-xs text-gray-400">
                Citas programadas
              </p>
              <Button className="w-full mt-4 bg-white/10 border border-white/20 text-white hover:bg-white/20">
                <Calendar className="mr-2 h-4 w-4" />
                Ver Citas
              </Button>
            </CardContent>
          </Card>

          {/* Mensajes */}
          <Card className="bg-white/5 border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white">Mensajes</CardTitle>
              <MessageSquare className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{messages.length}</div>
              <p className="text-xs text-gray-400">
                Mensajes nuevos
              </p>
              <Button className="w-full mt-4 bg-white/10 border border-white/20 text-white hover:bg-white/20">
                <MessageSquare className="mr-2 h-4 w-4" />
                Ver Mensajes
              </Button>
            </CardContent>
          </Card>

          {/* Perfil */}
          <Card className="bg-white/5 border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white">Mi Perfil</CardTitle>
              <User className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">100%</div>
              <p className="text-xs text-gray-400">
                Perfil completado
              </p>
              <Button className="w-full mt-4 bg-white/10 border border-white/20 text-white hover:bg-white/20">
                <User className="mr-2 h-4 w-4" />
                Editar Perfil
              </Button>
            </CardContent>
          </Card>
        </div>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mis Citas</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              Citas programadas
            </p>
            <Button className="w-full mt-4" variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Ver Citas
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensajes</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              Mensajes nuevos
            </p>
            <Button className="w-full mt-4" variant="outline">
              <MessageSquare className="mr-2 h-4 w-4" />
              Ver Mensajes
            </Button>
          </CardContent>
        </Card>
      </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Appointments */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Citas Recientes</CardTitle>
              <CardDescription className="text-gray-400">
                Tus últimas citas y próximas consultas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {appointments.length > 0 ? (
                  appointments.slice(0, 3).map((appointment) => (
                    <div key={appointment.id} className="flex items-center space-x-4 p-4 border border-white/10 rounded-lg bg-white/5">
                      <div className="flex-shrink-0">
                        <CheckCircle className="h-8 w-8 text-green-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-white">
                          {appointment.lawyerName || 'Consulta Legal'}
                        </p>
                        <p className="text-sm text-gray-400">
                          {appointment.specialty || 'Consulta General'} • {appointment.status || 'Pendiente'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {appointment.date || 'Fecha por confirmar'}
                        </p>
                      </div>
                      <Button size="sm" className="bg-white/10 border border-white/20 text-white hover:bg-white/20">
                        <Star className="h-4 w-4 mr-1" />
                        Reseña
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">No tienes citas recientes</p>
                  </div>
                )}

              </div>
            </CardContent>
          </Card>

          {/* Messages */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Mensajes Recientes</CardTitle>
              <CardDescription className="text-gray-400">
                Comunicación con tus abogados
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {messages.length > 0 ? (
                  messages.slice(0, 3).map((message) => (
                    <div key={message.id} className="flex items-start space-x-4 p-4 border border-white/10 rounded-lg bg-white/5">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center">
                          <MessageSquare className="h-4 w-4 text-yellow-500" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-white">
                            {message.lawyerName || 'Abogado'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {message.timestamp || 'Reciente'}
                          </p>
                        </div>
                        <p className="text-sm text-gray-400 mt-1">
                          {message.content || 'Nuevo mensaje disponible'}
                        </p>
                        <Button size="sm" className="mt-2 bg-white/10 border border-white/20 text-white hover:bg-white/20">
                          Responder
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">No tienes mensajes recientes</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Lawyers */}
        <div className="mt-8">
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Abogados Destacados</CardTitle>
              <CardDescription className="text-gray-400">
                Profesionales altamente calificados disponibles para consulta
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {lawyers.length > 0 ? (
                  lawyers.slice(0, 3).map((lawyer) => (
                    <div key={lawyer.id} className="border border-white/10 rounded-lg p-6 hover:bg-white/10 transition-all duration-300 bg-white/5">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center">
                          <Scale className="h-6 w-6 text-yellow-500" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">{lawyer.firstName} {lawyer.lastName}</h3>
                          <p className="text-sm text-gray-400">{lawyer.specialty || 'Consulta General'}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 mb-4">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className={`h-4 w-4 ${i < (lawyer.rating || 4) ? 'text-yellow-400 fill-current' : 'text-gray-600'}`} />
                          ))}
                        </div>
                        <span className="text-sm text-gray-400">{lawyer.rating || '4.8'} ({lawyer.reviews || '0'} reseñas)</span>
                      </div>
                      <p className="text-sm text-gray-400 mb-4">
                        {lawyer.description || 'Abogado profesional con experiencia en múltiples áreas del derecho.'}
                      </p>
                      <div className="flex items-center space-x-2 mb-4">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-400">{lawyer.location || 'Puerto Rico'}</span>
                      </div>
                      <Button className="w-full bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 hover:from-yellow-500 hover:via-orange-600 hover:to-yellow-500 text-black font-semibold">
                        <Briefcase className="mr-2 h-4 w-4" />
                        Agendar Cita
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="col-span-3 text-center py-8">
                    <Scale className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">Cargando abogados disponibles...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
