'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  MessageSquare,
  Search,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  User,
  LogOut,
  Scale,
  MapPin,
  Phone,
  Mail,
  Briefcase,
  Bell
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function ClientDashboard() {
  const { user, logout } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [messages, setMessages] = useState([]);
  const [lawyers, setLawyers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load user's appointments
      const appointmentsQuery = query(
        collection(db, 'appointments'),
        where('clientId', '==', user.id),
        orderBy('date', 'desc'),
        limit(5)
      );
      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      const appointmentsData = appointmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setAppointments(appointmentsData);

      // Load recent messages
      const messagesQuery = query(
        collection(db, 'messages'),
        where('clientId', '==', user.id),
        orderBy('timestamp', 'desc'),
        limit(5)
      );
      const messagesSnapshot = await getDocs(messagesQuery);
      const messagesData = messagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setMessages(messagesData);

      // Load featured lawyers
      const lawyersQuery = query(
        collection(db, 'lawyers'),
        where('isActive', '==', true),
        orderBy('rating', 'desc'),
        limit(6)
      );
      const lawyersSnapshot = await getDocs(lawyersQuery);
      const lawyersData = lawyersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setLawyers(lawyersData);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-800/30"></div>
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-br from-yellow-500/3 via-transparent to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-1/3 bg-gradient-to-tl from-amber-600/3 via-transparent to-transparent"></div>
      </div>

      <div className="relative z-10 p-8">
        {/* Top Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar abogados, especialidades, o servicios legales..."
              className="w-full pl-12 pr-4 py-4 bg-black/40 backdrop-blur-xl border border-yellow-500/30 rounded-2xl text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 transition-all duration-300"
            />
            <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-semibold px-6 py-2 rounded-xl shadow-lg hover:shadow-yellow-500/25 transition-all duration-300">
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Header */}
        <div className="flex justify-between items-center mb-10">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent">
              ¡Bienvenido/a, {user?.firstName}!
            </h1>
            <p className="mt-3 text-gray-300 text-lg">
              Gestiona tus citas legales y encuentra los mejores abogados en Puerto Rico.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Button className="bg-black/40 backdrop-blur-xl border border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10 hover:border-yellow-500/50 rounded-xl px-4 py-2 transition-all duration-300">
                <Bell className="h-5 w-5" />
              </Button>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full animate-pulse"></div>
            </div>
            <Button
              onClick={handleLogout}
              className="bg-black/40 backdrop-blur-xl border border-red-500/30 text-red-400 hover:bg-red-500/10 hover:border-red-500/50 rounded-xl px-4 py-2 transition-all duration-300"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Cerrar Sesión
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Buscar Abogados */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 hover:border-yellow-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-yellow-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Search className="h-7 w-7 text-yellow-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent">{lawyers.length}+</div>
                  <p className="text-xs text-gray-400 font-medium">Disponibles</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Buscar Abogados</h3>
              <p className="text-sm text-gray-400 mb-6">Encuentra especialistas en tu área legal</p>
              <Button className="w-full bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold py-3 rounded-2xl shadow-lg hover:shadow-yellow-500/25 transition-all duration-300 group-hover:scale-105">
                <Search className="mr-2 h-4 w-4" />
                Buscar Ahora
              </Button>
            </CardContent>
          </Card>

          {/* Citas */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-blue-500/20 hover:border-blue-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Calendar className="h-7 w-7 text-blue-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-blue-500 bg-clip-text text-transparent">{appointments.length}</div>
                  <p className="text-xs text-gray-400 font-medium">Programadas</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Mis Citas</h3>
              <p className="text-sm text-gray-400 mb-6">Gestiona tus consultas legales</p>
              <Button className="w-full bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 hover:border-blue-500/50 font-bold py-3 rounded-2xl transition-all duration-300 group-hover:scale-105">
                <Calendar className="mr-2 h-4 w-4" />
                Ver Citas
              </Button>
            </CardContent>
          </Card>

          {/* Mensajes */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-green-500/20 hover:border-green-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-green-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <MessageSquare className="h-7 w-7 text-green-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-green-500 bg-clip-text text-transparent">{messages.length}</div>
                  <p className="text-xs text-gray-400 font-medium">Nuevos</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Mensajes</h3>
              <p className="text-sm text-gray-400 mb-6">Comunícate con abogados</p>
              <Button className="w-full bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 hover:border-green-500/50 font-bold py-3 rounded-2xl transition-all duration-300 group-hover:scale-105">
                <MessageSquare className="mr-2 h-4 w-4" />
                Ver Mensajes
              </Button>
            </CardContent>
          </Card>

          {/* Perfil */}
          <Card className="group bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-purple-500/20 hover:border-purple-500/40 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/10 cursor-pointer">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="w-14 h-14 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <User className="h-7 w-7 text-purple-400" />
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-purple-500 bg-clip-text text-transparent">100%</div>
                  <p className="text-xs text-gray-400 font-medium">Completo</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Mi Perfil</h3>
              <p className="text-sm text-gray-400 mb-6">Actualiza tu información</p>
              <Button className="w-full bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-500/30 text-purple-400 hover:bg-purple-500/30 hover:border-purple-500/50 font-bold py-3 rounded-2xl transition-all duration-300 group-hover:scale-105">
                <User className="mr-2 h-4 w-4" />
                Editar Perfil
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Appointments & Messages */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Recent Appointments */}
          <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-yellow-500/10 to-amber-500/10 border-b border-yellow-500/20 p-8">
              <CardTitle className="flex items-center text-white text-xl font-bold">
                <div className="w-10 h-10 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center mr-3">
                  <Calendar className="h-5 w-5 text-yellow-400" />
                </div>
                Próximas Citas
              </CardTitle>
              <CardDescription className="text-gray-300 text-base">
                Tus citas programadas para los próximos días
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8 space-y-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
                  <p className="text-gray-400 mt-4 text-lg">Cargando citas...</p>
                </div>
              ) : appointments.length > 0 ? (
                appointments.slice(0, 3).map((appointment) => (
                  <div key={appointment.id} className="group flex items-center space-x-4 p-6 border border-white/10 rounded-2xl bg-gradient-to-r from-white/5 to-white/10 hover:from-yellow-500/10 hover:to-amber-500/10 hover:border-yellow-500/30 transition-all duration-300">
                    <div className="flex-shrink-0">
                      <div className="w-14 h-14 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <Scale className="h-6 w-6 text-yellow-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-base font-bold text-white mb-1">
                        {appointment.lawyerName || 'Consulta Legal'}
                      </p>
                      <p className="text-sm text-gray-300">
                        {appointment.specialty || 'Consulta General'}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {appointment.date || 'Fecha por confirmar'}
                      </p>
                    </div>
                    <div className="flex flex-col space-y-2">
                      <Badge className="bg-gradient-to-r from-green-400/20 to-green-500/20 border border-green-400/30 text-green-400 px-3 py-1 rounded-xl font-medium">
                        {appointment.status || 'Pendiente'}
                      </Badge>
                      <Button size="sm" className="bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/30 hover:border-yellow-400/50 rounded-xl">
                        <Star className="h-3 w-3 mr-1" />
                        Reseña
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
                    <Calendar className="h-10 w-10 text-gray-500" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">No tienes citas programadas</h3>
                  <p className="text-gray-400 mb-6">Agenda tu primera consulta legal</p>
                  <Button className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-8 py-3 rounded-2xl shadow-lg hover:shadow-yellow-500/25 transition-all duration-300">
                    <Plus className="mr-2 h-5 w-5" />
                    Agendar Primera Cita
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Messages */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Mensajes Recientes</CardTitle>
              <CardDescription className="text-gray-400">
                Comunicación con tus abogados
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {messages.length > 0 ? (
                  messages.slice(0, 3).map((message) => (
                    <div key={message.id} className="flex items-start space-x-4 p-4 border border-white/10 rounded-lg bg-white/5">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center">
                          <MessageSquare className="h-4 w-4 text-yellow-500" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-white">
                            {message.lawyerName || 'Abogado'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {message.timestamp || 'Reciente'}
                          </p>
                        </div>
                        <p className="text-sm text-gray-400 mt-1">
                          {message.content || 'Nuevo mensaje disponible'}
                        </p>
                        <Button size="sm" className="mt-2 bg-white/10 border border-white/20 text-white hover:bg-white/20">
                          Responder
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">No tienes mensajes recientes</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Lawyers */}
        <div className="mt-8">
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Abogados Destacados</CardTitle>
              <CardDescription className="text-gray-400">
                Profesionales altamente calificados disponibles para consulta
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {lawyers.length > 0 ? (
                  lawyers.slice(0, 3).map((lawyer) => (
                    <div key={lawyer.id} className="border border-white/10 rounded-lg p-6 hover:bg-white/10 transition-all duration-300 bg-white/5">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center">
                          <Scale className="h-6 w-6 text-yellow-500" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">{lawyer.firstName} {lawyer.lastName}</h3>
                          <p className="text-sm text-gray-400">{lawyer.specialty || 'Consulta General'}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 mb-4">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className={`h-4 w-4 ${i < (lawyer.rating || 4) ? 'text-yellow-400 fill-current' : 'text-gray-600'}`} />
                          ))}
                        </div>
                        <span className="text-sm text-gray-400">{lawyer.rating || '4.8'} ({lawyer.reviews || '0'} reseñas)</span>
                      </div>
                      <p className="text-sm text-gray-400 mb-4">
                        {lawyer.description || 'Abogado profesional con experiencia en múltiples áreas del derecho.'}
                      </p>
                      <div className="flex items-center space-x-2 mb-4">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-400">{lawyer.location || 'Puerto Rico'}</span>
                      </div>
                      <Button className="w-full bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 hover:from-yellow-500 hover:via-orange-600 hover:to-yellow-500 text-black font-semibold">
                        <Briefcase className="mr-2 h-4 w-4" />
                        Agendar Cita
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="col-span-3 text-center py-8">
                    <Scale className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">Cargando abogados disponibles...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
