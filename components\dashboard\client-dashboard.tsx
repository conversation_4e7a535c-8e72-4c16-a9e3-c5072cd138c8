'use client';

import React from 'react';
import { useAuth } from '@/components/providers/auth-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  MessageSquare, 
  Search, 
  Star, 
  Clock,
  CheckCircle,
  AlertCircle,
  Plus
} from 'lucide-react';

export function ClientDashboard() {
  const { user } = useAuth();

  return (
    <div className="p-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          ¡Bienvenido/a, {user?.firstName}!
        </h1>
        <p className="mt-2 text-gray-600">
          Gestiona tus citas legales y encuentra los mejores abogados en Puerto Rico.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Buscar Abogados</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">500+</div>
            <p className="text-xs text-muted-foreground">
              Abogados disponibles
            </p>
            <Button className="w-full mt-4" variant="legal">
              <Search className="mr-2 h-4 w-4" />
              Buscar Ahora
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mis Citas</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              Citas programadas
            </p>
            <Button className="w-full mt-4" variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Ver Citas
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensajes</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              Mensajes nuevos
            </p>
            <Button className="w-full mt-4" variant="outline">
              <MessageSquare className="mr-2 h-4 w-4" />
              Ver Mensajes
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Appointments */}
        <Card>
          <CardHeader>
            <CardTitle>Citas Recientes</CardTitle>
            <CardDescription>
              Tus últimas citas y próximas consultas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Consulta con Dra. María González
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho de Familia • Completada
                  </p>
                  <p className="text-xs text-gray-400">
                    15 de Diciembre, 2024
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  <Star className="h-4 w-4 mr-1" />
                  Reseña
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Consulta con Lic. Carlos Rodríguez
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho Inmobiliario • Programada
                  </p>
                  <p className="text-xs text-gray-400">
                    22 de Diciembre, 2024 - 2:00 PM
                  </p>
                </div>
                <Button size="sm" variant="legal">
                  Ver Detalles
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-8 w-8 text-orange-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Consulta con Lic. Ana Martínez
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho Laboral • Pendiente confirmación
                  </p>
                  <p className="text-xs text-gray-400">
                    28 de Diciembre, 2024 - 10:00 AM
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  Confirmar
                </Button>
              </div>
            </div>

            <Button className="w-full mt-4" variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Programar Nueva Cita
            </Button>
          </CardContent>
        </Card>

        {/* Recommended Lawyers */}
        <Card>
          <CardHeader>
            <CardTitle>Abogados Recomendados</CardTitle>
            <CardDescription>
              Profesionales destacados en tu área
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">MG</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Dra. María González
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho de Familia
                  </p>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-3 w-3 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 ml-1">(4.9)</span>
                  </div>
                </div>
                <Button size="sm" variant="legal">
                  Ver Perfil
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="text-green-600 font-semibold">CR</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Lic. Carlos Rodríguez
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho Inmobiliario
                  </p>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className={`h-3 w-3 ${i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 ml-1">(4.7)</span>
                  </div>
                </div>
                <Button size="sm" variant="legal">
                  Ver Perfil
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                    <span className="text-purple-600 font-semibold">AM</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Lic. Ana Martínez
                  </p>
                  <p className="text-sm text-gray-500">
                    Derecho Laboral
                  </p>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-3 w-3 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 ml-1">(5.0)</span>
                  </div>
                </div>
                <Button size="sm" variant="legal">
                  Ver Perfil
                </Button>
              </div>
            </div>

            <Button className="w-full mt-4" variant="outline">
              Ver Todos los Abogados
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
