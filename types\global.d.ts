// Global type declarations for next-intl
declare global {
  // Define IntlMessages type for next-intl
  interface IntlMessages {
    [key: string]: any;
  }

  // Define IntlFormats type for next-intl
  interface IntlFormats {
    dateTime: Record<string, Intl.DateTimeFormatOptions>;
    number: Record<string, Intl.NumberFormatOptions>;
    list: Record<string, Intl.ListFormatOptions>;
  }
}

// Extend Intl namespace to include ListFormat
declare namespace Intl {
  interface ListFormatOptions {
    localeMatcher?: 'lookup' | 'best fit';
    type?: 'conjunction' | 'disjunction' | 'unit';
    style?: 'long' | 'short' | 'narrow';
  }

  interface ListFormat {
    format(list: Iterable<string>): string;
    formatToParts(list: Iterable<string>): Array<{type: string, value: string}>;
  }

  const ListFormat: {
    new (locales?: string | string[], options?: ListFormatOptions): ListFormat;
    prototype: ListFormat;
    supportedLocalesOf(locales: string | string[], options?: { localeMatcher?: string }): string[];
  };
}

export {};
