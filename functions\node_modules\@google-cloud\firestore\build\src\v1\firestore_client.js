"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirestoreClient = void 0;
const stream_1 = require("stream");
const jsonProtos = require("../../protos/v1.json");
const logger_1 = require("../logger");
/**
 * Client JSON configuration object, loaded from
 * `src/v1/firestore_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./firestore_client_config.json");
const version = require('../../../package.json').version;
/**
 *  The Cloud Firestore service.
 *
 *  Cloud Firestore is a fast, fully managed, serverless, cloud-native NoSQL
 *  document database that simplifies storing, syncing, and querying data for
 *  your mobile, web, and IoT apps at global scale. Its client libraries provide
 *  live synchronization and offline support, while its security features and
 *  integrations with Firebase and Google Cloud Platform accelerate building
 *  truly serverless apps.
 * @class
 * @memberof v1
 */
class FirestoreClient {
    /**
     * Construct an instance of FirestoreClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new FirestoreClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        var _a, _b, _c, _d, _e;
        this._terminated = false;
        this._stubFailed = false;
        this.descriptors = {
            page: {},
            stream: {},
            longrunning: {},
            batching: {},
        };
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if ((opts === null || opts === void 0 ? void 0 : opts.universe_domain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universeDomain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== (opts === null || opts === void 0 ? void 0 : opts.universeDomain)) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            (_c = (_b = (_a = opts === null || opts === void 0 ? void 0 : opts.universeDomain) !== null && _a !== void 0 ? _a : opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== null && _b !== void 0 ? _b : universeDomainEnvVar) !== null && _c !== void 0 ? _c : 'googleapis.com';
        this._servicePath = 'firestore.' + this._universeDomain;
        const servicePath = (opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint) || this._servicePath;
        this._providedCustomServicePath = !!((opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint));
        const port = (opts === null || opts === void 0 ? void 0 : opts.port) || staticMembers.port;
        const clientConfig = (_d = opts === null || opts === void 0 ? void 0 : opts.clientConfig) !== null && _d !== void 0 ? _d : {};
        const fallback = (_e = opts === null || opts === void 0 ? void 0 : opts.fallback) !== null && _e !== void 0 ? _e : (typeof window !== 'undefined' && typeof (window === null || window === void 0 ? void 0 : window.fetch) === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listDocuments: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'documents'),
            partitionQuery: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'partitions'),
            listCollectionIds: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'collectionIds'),
        };
        // Some of the methods on this service provide streaming responses.
        // Provide descriptors for these.
        this.descriptors.stream = {
            batchGetDocuments: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.SERVER_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
            runQuery: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.SERVER_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
            runAggregationQuery: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.SERVER_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
            write: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.BIDI_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
            listen: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.BIDI_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.firestore.v1.Firestore', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.firestoreStub && !this._stubFailed) {
            return this.firestoreStub;
        }
        // Reset _stubFailed because we are re-attempting create
        this._stubFailed = false;
        // Put together the "service stub" for
        // google.firestore.v1.Firestore.
        this.firestoreStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.firestore.v1.Firestore')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.firestore.v1.Firestore, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const firestoreStubMethods = [
            'getDocument',
            'listDocuments',
            'updateDocument',
            'deleteDocument',
            'batchGetDocuments',
            'beginTransaction',
            'commit',
            'rollback',
            'runQuery',
            'runAggregationQuery',
            'partitionQuery',
            'write',
            'listen',
            'listCollectionIds',
            'batchWrite',
            'createDocument',
        ];
        for (const methodName of firestoreStubMethods) {
            const callPromise = this.firestoreStub.then(stub => {
                return (...args) => {
                    if (this._terminated) {
                        if (methodName in this.descriptors.stream) {
                            const stream = new stream_1.PassThrough({ objectMode: true });
                            setImmediate(() => {
                                stream.emit('error', new this._gaxModule.GoogleError('The client has already been closed.'));
                            });
                            return stream;
                        }
                        return Promise.reject('The client has already been closed.');
                    }
                    const func = stub[methodName];
                    return func.apply(stub, args);
                };
            }, (err) => {
                this._stubFailed = true;
                (0, logger_1.logger)('initialize', null, 'Failed to create the gax client stub.', err);
                return () => {
                    throw err;
                };
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.stream[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.firestoreStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'firestore.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'firestore.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [
            'https://www.googleapis.com/auth/cloud-platform',
            'https://www.googleapis.com/auth/datastore',
        ];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    getDocument(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getDocument(request, options, callback);
    }
    updateDocument(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'document.name': (_a = request.document.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateDocument(request, options, callback);
    }
    deleteDocument(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteDocument(request, options, callback);
    }
    beginTransaction(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                database: (_a = request.database) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.beginTransaction(request, options, callback);
    }
    commit(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                database: (_a = request.database) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.commit(request, options, callback);
    }
    rollback(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                database: (_a = request.database) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.rollback(request, options, callback);
    }
    batchWrite(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                database: (_a = request.database) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.batchWrite(request, options, callback);
    }
    createDocument(request, optionsOrCallback, callback) {
        var _a, _b;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
                collection_id: (_b = request.collectionId) !== null && _b !== void 0 ? _b : '',
            });
        this.initialize();
        return this.innerApiCalls.createDocument(request, options, callback);
    }
    /**
     * Gets multiple documents.
     *
     * Documents returned by this method are not guaranteed to be returned in the
     * same order that they were requested.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.database
     *   Required. The database name. In the format:
     *   `projects/{project_id}/databases/{database_id}`.
     * @param {string[]} request.documents
     *   The names of the documents to retrieve. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
     *   The request will fail if any of the document is not a child resource of the
     *   given `database`. Duplicate names will be elided.
     * @param {google.firestore.v1.DocumentMask} request.mask
     *   The fields to return. If not set, returns all fields.
     *
     *   If a document has a field that is not present in this mask, that field will
     *   not be returned in the response.
     * @param {Buffer} request.transaction
     *   Reads documents in a transaction.
     * @param {google.firestore.v1.TransactionOptions} request.newTransaction
     *   Starts a new transaction and reads the documents.
     *   Defaults to a read-only transaction.
     *   The new transaction ID will be returned as the first response in the
     *   stream.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Reads documents as they were at the given time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits {@link protos.google.firestore.v1.BatchGetDocumentsResponse|BatchGetDocumentsResponse} on 'data' event.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#server-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.batch_get_documents.js</caption>
     * region_tag:firestore_v1_generated_Firestore_BatchGetDocuments_async
     */
    batchGetDocuments(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                database: (_a = request.database) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.batchGetDocuments(request, options);
    }
    /**
     * Runs a query.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent resource name. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents` or
     *   `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
     *   For example:
     *   `projects/my-project/databases/my-database/documents` or
     *   `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`
     * @param {google.firestore.v1.StructuredQuery} request.structuredQuery
     *   A structured query.
     * @param {Buffer} request.transaction
     *   Run the query within an already active transaction.
     *
     *   The value here is the opaque transaction ID to execute the query in.
     * @param {google.firestore.v1.TransactionOptions} request.newTransaction
     *   Starts a new transaction and reads the documents.
     *   Defaults to a read-only transaction.
     *   The new transaction ID will be returned as the first response in the
     *   stream.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Reads documents as they were at the given time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {google.firestore.v1.ExplainOptions} [request.explainOptions]
     *   Optional. Explain options for the query. If set, additional query
     *   statistics will be returned. If not, only query results will be returned.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits {@link protos.google.firestore.v1.RunQueryResponse|RunQueryResponse} on 'data' event.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#server-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.run_query.js</caption>
     * region_tag:firestore_v1_generated_Firestore_RunQuery_async
     */
    runQuery(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.runQuery(request, options);
    }
    /**
     * Runs an aggregation query.
     *
     * Rather than producing {@link protos.google.firestore.v1.Document|Document} results like
     * {@link protos.google.firestore.v1.Firestore.RunQuery|Firestore.RunQuery}, this API
     * allows running an aggregation to produce a series of
     * {@link protos.google.firestore.v1.AggregationResult|AggregationResult} server-side.
     *
     * High-Level Example:
     *
     * ```
     * -- Return the number of documents in table given a filter.
     * SELECT COUNT(*) FROM ( SELECT * FROM k where a = true );
     * ```
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent resource name. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents` or
     *   `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
     *   For example:
     *   `projects/my-project/databases/my-database/documents` or
     *   `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`
     * @param {google.firestore.v1.StructuredAggregationQuery} request.structuredAggregationQuery
     *   An aggregation query.
     * @param {Buffer} request.transaction
     *   Run the aggregation within an already active transaction.
     *
     *   The value here is the opaque transaction ID to execute the query in.
     * @param {google.firestore.v1.TransactionOptions} request.newTransaction
     *   Starts a new transaction as part of the query, defaulting to read-only.
     *
     *   The new transaction ID will be returned as the first response in the
     *   stream.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Executes the query at the given timestamp.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {google.firestore.v1.ExplainOptions} [request.explainOptions]
     *   Optional. Explain options for the query. If set, additional query
     *   statistics will be returned. If not, only query results will be returned.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits {@link protos.google.firestore.v1.RunAggregationQueryResponse|RunAggregationQueryResponse} on 'data' event.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#server-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.run_aggregation_query.js</caption>
     * region_tag:firestore_v1_generated_Firestore_RunAggregationQuery_async
     */
    runAggregationQuery(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.runAggregationQuery(request, options);
    }
    /**
     * Streams batches of document updates and deletes, in order. This method is
     * only available via gRPC or WebChannel (not REST).
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.firestore.v1.WriteRequest|WriteRequest} for write() method, and
     *   will emit objects representing {@link protos.google.firestore.v1.WriteResponse|WriteResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.write.js</caption>
     * region_tag:firestore_v1_generated_Firestore_Write_async
     */
    write(options) {
        this.initialize();
        return this.innerApiCalls.write(null, options);
    }
    /**
     * Listens to changes. This method is only available via gRPC or WebChannel
     * (not REST).
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.firestore.v1.ListenRequest|ListenRequest} for write() method, and
     *   will emit objects representing {@link protos.google.firestore.v1.ListenResponse|ListenResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.listen.js</caption>
     * region_tag:firestore_v1_generated_Firestore_Listen_async
     */
    listen(options) {
        this.initialize();
        return this.innerApiCalls.listen(null, options);
    }
    listDocuments(request, optionsOrCallback, callback) {
        var _a, _b;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
                collection_id: (_b = request.collectionId) !== null && _b !== void 0 ? _b : '',
            });
        this.initialize();
        return this.innerApiCalls.listDocuments(request, options, callback);
    }
    /**
     * Equivalent to `listDocuments`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent resource name. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents` or
     *   `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
     *
     *   For example:
     *   `projects/my-project/databases/my-database/documents` or
     *   `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`
     * @param {string} [request.collectionId]
     *   Optional. The collection ID, relative to `parent`, to list.
     *
     *   For example: `chatrooms` or `messages`.
     *
     *   This is optional, and when not provided, Firestore will list documents
     *   from all collections under the provided `parent`.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of documents to return in a single response.
     *
     *   Firestore may return fewer than this value.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListDocuments` response.
     *
     *   Provide this to retrieve the subsequent page. When paginating, all other
     *   parameters (with the exception of `page_size`) must match the values set
     *   in the request that generated the page token.
     * @param {string} [request.orderBy]
     *   Optional. The optional ordering of the documents to return.
     *
     *   For example: `priority desc, __name__ desc`.
     *
     *   This mirrors the {@link protos.google.firestore.v1.StructuredQuery.order_by|`ORDER BY`}
     *   used in Firestore queries but in a string representation. When absent,
     *   documents are ordered based on `__name__ ASC`.
     * @param {google.firestore.v1.DocumentMask} [request.mask]
     *   Optional. The fields to return. If not set, returns all fields.
     *
     *   If a document has a field that is not present in this mask, that field
     *   will not be returned in the response.
     * @param {Buffer} request.transaction
     *   Perform the read as part of an already active transaction.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Perform the read at the provided time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {boolean} request.showMissing
     *   If the list should show missing documents.
     *
     *   A document is missing if it does not exist, but there are sub-documents
     *   nested underneath it. When true, such missing documents will be returned
     *   with a key but will not have fields,
     *   {@link protos.google.firestore.v1.Document.create_time|`create_time`}, or
     *   {@link protos.google.firestore.v1.Document.update_time|`update_time`} set.
     *
     *   Requests with `show_missing` may not specify `where` or `order_by`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.firestore.v1.Document|Document} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listDocumentsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listDocumentsStream(request, options) {
        var _a, _b;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
                collection_id: (_b = request.collectionId) !== null && _b !== void 0 ? _b : '',
            });
        const defaultCallSettings = this._defaults['listDocuments'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listDocuments.createStream(this.innerApiCalls.listDocuments, request, callSettings);
    }
    /**
     * Equivalent to `listDocuments`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent resource name. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents` or
     *   `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
     *
     *   For example:
     *   `projects/my-project/databases/my-database/documents` or
     *   `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`
     * @param {string} [request.collectionId]
     *   Optional. The collection ID, relative to `parent`, to list.
     *
     *   For example: `chatrooms` or `messages`.
     *
     *   This is optional, and when not provided, Firestore will list documents
     *   from all collections under the provided `parent`.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of documents to return in a single response.
     *
     *   Firestore may return fewer than this value.
     * @param {string} [request.pageToken]
     *   Optional. A page token, received from a previous `ListDocuments` response.
     *
     *   Provide this to retrieve the subsequent page. When paginating, all other
     *   parameters (with the exception of `page_size`) must match the values set
     *   in the request that generated the page token.
     * @param {string} [request.orderBy]
     *   Optional. The optional ordering of the documents to return.
     *
     *   For example: `priority desc, __name__ desc`.
     *
     *   This mirrors the {@link protos.google.firestore.v1.StructuredQuery.order_by|`ORDER BY`}
     *   used in Firestore queries but in a string representation. When absent,
     *   documents are ordered based on `__name__ ASC`.
     * @param {google.firestore.v1.DocumentMask} [request.mask]
     *   Optional. The fields to return. If not set, returns all fields.
     *
     *   If a document has a field that is not present in this mask, that field
     *   will not be returned in the response.
     * @param {Buffer} request.transaction
     *   Perform the read as part of an already active transaction.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Perform the read at the provided time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {boolean} request.showMissing
     *   If the list should show missing documents.
     *
     *   A document is missing if it does not exist, but there are sub-documents
     *   nested underneath it. When true, such missing documents will be returned
     *   with a key but will not have fields,
     *   {@link protos.google.firestore.v1.Document.create_time|`create_time`}, or
     *   {@link protos.google.firestore.v1.Document.update_time|`update_time`} set.
     *
     *   Requests with `show_missing` may not specify `where` or `order_by`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.firestore.v1.Document|Document}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.list_documents.js</caption>
     * region_tag:firestore_v1_generated_Firestore_ListDocuments_async
     */
    listDocumentsAsync(request, options) {
        var _a, _b;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
                collection_id: (_b = request.collectionId) !== null && _b !== void 0 ? _b : '',
            });
        const defaultCallSettings = this._defaults['listDocuments'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listDocuments.asyncIterate(this.innerApiCalls['listDocuments'], request, callSettings);
    }
    partitionQuery(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.partitionQuery(request, options, callback);
    }
    /**
     * Equivalent to `partitionQuery`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent resource name. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents`.
     *   Document resource names are not supported; only database resource names
     *   can be specified.
     * @param {google.firestore.v1.StructuredQuery} request.structuredQuery
     *   A structured query.
     *   Query must specify collection with all descendants and be ordered by name
     *   ascending. Other filters, order bys, limits, offsets, and start/end
     *   cursors are not supported.
     * @param {number} request.partitionCount
     *   The desired maximum number of partition points.
     *   The partitions may be returned across multiple pages of results.
     *   The number must be positive. The actual number of partitions
     *   returned may be fewer.
     *
     *   For example, this may be set to one fewer than the number of parallel
     *   queries to be run, or in running a data pipeline job, one fewer than the
     *   number of workers or compute instances available.
     * @param {string} request.pageToken
     *   The `next_page_token` value returned from a previous call to
     *   PartitionQuery that may be used to get an additional set of results.
     *   There are no ordering guarantees between sets of results. Thus, using
     *   multiple sets of results will require merging the different result sets.
     *
     *   For example, two subsequent calls using a page_token may return:
     *
     *    * cursor B, cursor M, cursor Q
     *    * cursor A, cursor U, cursor W
     *
     *   To obtain a complete result set ordered with respect to the results of the
     *   query supplied to PartitionQuery, the results sets should be merged:
     *   cursor A, cursor B, cursor M, cursor Q, cursor U, cursor W
     * @param {number} request.pageSize
     *   The maximum number of partitions to return in this call, subject to
     *   `partition_count`.
     *
     *   For example, if `partition_count` = 10 and `page_size` = 8, the first call
     *   to PartitionQuery will return up to 8 partitions and a `next_page_token`
     *   if more results exist. A second call to PartitionQuery will return up to
     *   2 partitions, to complete the total of 10 specified in `partition_count`.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Reads documents as they were at the given time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.firestore.v1.Cursor|Cursor} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `partitionQueryAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    partitionQueryStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['partitionQuery'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.partitionQuery.createStream(this.innerApiCalls.partitionQuery, request, callSettings);
    }
    /**
     * Equivalent to `partitionQuery`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent resource name. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents`.
     *   Document resource names are not supported; only database resource names
     *   can be specified.
     * @param {google.firestore.v1.StructuredQuery} request.structuredQuery
     *   A structured query.
     *   Query must specify collection with all descendants and be ordered by name
     *   ascending. Other filters, order bys, limits, offsets, and start/end
     *   cursors are not supported.
     * @param {number} request.partitionCount
     *   The desired maximum number of partition points.
     *   The partitions may be returned across multiple pages of results.
     *   The number must be positive. The actual number of partitions
     *   returned may be fewer.
     *
     *   For example, this may be set to one fewer than the number of parallel
     *   queries to be run, or in running a data pipeline job, one fewer than the
     *   number of workers or compute instances available.
     * @param {string} request.pageToken
     *   The `next_page_token` value returned from a previous call to
     *   PartitionQuery that may be used to get an additional set of results.
     *   There are no ordering guarantees between sets of results. Thus, using
     *   multiple sets of results will require merging the different result sets.
     *
     *   For example, two subsequent calls using a page_token may return:
     *
     *    * cursor B, cursor M, cursor Q
     *    * cursor A, cursor U, cursor W
     *
     *   To obtain a complete result set ordered with respect to the results of the
     *   query supplied to PartitionQuery, the results sets should be merged:
     *   cursor A, cursor B, cursor M, cursor Q, cursor U, cursor W
     * @param {number} request.pageSize
     *   The maximum number of partitions to return in this call, subject to
     *   `partition_count`.
     *
     *   For example, if `partition_count` = 10 and `page_size` = 8, the first call
     *   to PartitionQuery will return up to 8 partitions and a `next_page_token`
     *   if more results exist. A second call to PartitionQuery will return up to
     *   2 partitions, to complete the total of 10 specified in `partition_count`.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Reads documents as they were at the given time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.firestore.v1.Cursor|Cursor}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.partition_query.js</caption>
     * region_tag:firestore_v1_generated_Firestore_PartitionQuery_async
     */
    partitionQueryAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['partitionQuery'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.partitionQuery.asyncIterate(this.innerApiCalls['partitionQuery'], request, callSettings);
    }
    listCollectionIds(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listCollectionIds(request, options, callback);
    }
    /**
     * Equivalent to `listCollectionIds`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent document. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
     *   For example:
     *   `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`
     * @param {number} request.pageSize
     *   The maximum number of results to return.
     * @param {string} request.pageToken
     *   A page token. Must be a value from
     *   {@link protos.google.firestore.v1.ListCollectionIdsResponse|ListCollectionIdsResponse}.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Reads documents as they were at the given time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing string on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listCollectionIdsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listCollectionIdsStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listCollectionIds'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listCollectionIds.createStream(this.innerApiCalls.listCollectionIds, request, callSettings);
    }
    /**
     * Equivalent to `listCollectionIds`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent document. In the format:
     *   `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
     *   For example:
     *   `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`
     * @param {number} request.pageSize
     *   The maximum number of results to return.
     * @param {string} request.pageToken
     *   A page token. Must be a value from
     *   {@link protos.google.firestore.v1.ListCollectionIdsResponse|ListCollectionIdsResponse}.
     * @param {google.protobuf.Timestamp} request.readTime
     *   Reads documents as they were at the given time.
     *
     *   This must be a microsecond precision timestamp within the past one hour,
     *   or if Point-in-Time Recovery is enabled, can additionally be a whole
     *   minute timestamp within the past 7 days.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   string. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore.list_collection_ids.js</caption>
     * region_tag:firestore_v1_generated_Firestore_ListCollectionIds_async
     */
    listCollectionIdsAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listCollectionIds'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listCollectionIds.asyncIterate(this.innerApiCalls['listCollectionIds'], request, callSettings);
    }
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.firestoreStub && !this._terminated) {
            return this.firestoreStub.then(stub => {
                this._terminated = true;
                stub.close();
                this.locationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.FirestoreClient = FirestoreClient;
//# sourceMappingURL=firestore_client.js.map