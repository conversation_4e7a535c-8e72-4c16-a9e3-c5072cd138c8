import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Scale } from 'lucide-react';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 p-4">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-yellow-500/5 to-yellow-600/5 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-float"></div>
        <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-yellow-600/5 to-amber-700/5 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="text-center max-w-md space-y-6 relative z-10">
        <div className="flex items-center justify-center mb-6">
          <Scale className="h-12 w-12 text-yellow-400" />
          <span className="ml-3 text-3xl font-bold bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-400 bg-clip-text text-transparent">LegalPR</span>
        </div>
        
        <h1 className="text-6xl font-bold text-yellow-400">404</h1>
        <h2 className="text-3xl font-semibold text-white">
          Page Not Found
        </h2>
        <p className="text-lg text-gray-300">
          The page you are looking for does not exist.
        </p>
        <div className="pt-4">
          <Link href="/">
            <Button className="bg-gradient-to-r from-yellow-500 via-yellow-400 to-yellow-600 hover:from-yellow-400 hover:via-yellow-300 hover:to-yellow-500 text-gray-900 font-medium">
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
