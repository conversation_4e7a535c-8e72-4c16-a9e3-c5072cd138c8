import { setRequestLocale } from 'next-intl/server';
import { ClientDashboardLayout } from '@/components/dashboard/client-dashboard-layout';

// Force dynamic rendering to prevent Firebase initialization during build
export const dynamic = 'force-dynamic';

interface ClientDashboardPageProps {
  params: { locale: string };
}

export default function ClientDashboard({ params: { locale } }: ClientDashboardPageProps) {
  setRequestLocale(locale);

  return <ClientDashboardLayout />;
}
