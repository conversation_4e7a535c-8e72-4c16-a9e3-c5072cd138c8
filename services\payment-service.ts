import {
  collection,
  addDoc,
  updateDoc,
  doc,
  getDoc,
  serverTimestamp,
  query,
  where,
  getDocs
} from 'firebase/firestore';
import { getFirebaseDb } from '@/lib/firebase';
import { NotificationService } from './notification-service';

export interface PaymentData {
  id?: string;
  clientId: string;
  lawyerId: string;
  appointmentId?: string;
  amount: number;
  currency: string;
  description: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled' | 'refunded';
  paymentIntentId?: string;
  stripeSessionId?: string;
  paymentMethod?: string;
  receiptUrl?: string;
  refundId?: string;
  platformFee: number;
  lawyerAmount: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
  failedAt?: string;
  refundedAt?: string;
}

export interface AppointmentPayment {
  appointmentId: string;
  clientId: string;
  lawyerId: string;
  lawyerName: string;
  clientName: string;
  appointmentDate: string;
  appointmentTime: string;
  specialty: string;
  amount: number;
  description: string;
}

export class PaymentService {
  
  // Create a payment record in Firestore
  static async createPayment(paymentData: Omit<PaymentData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const payment = {
        ...paymentData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const docRef = await addDoc(collection(getFirebaseDb(), 'payments'), {
        ...payment,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return docRef.id;
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  }

  // Update payment status
  static async updatePaymentStatus(
    paymentId: string, 
    status: PaymentData['status'], 
    additionalData?: Partial<PaymentData>
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: serverTimestamp(),
        ...additionalData
      };

      // Add timestamp for specific status changes
      if (status === 'succeeded') {
        updateData.paidAt = serverTimestamp();
      } else if (status === 'failed') {
        updateData.failedAt = serverTimestamp();
      } else if (status === 'refunded') {
        updateData.refundedAt = serverTimestamp();
      }

      await updateDoc(doc(getFirebaseDb(), 'payments', paymentId), updateData);

      // Send notifications based on status
      const paymentDoc = await getDoc(doc(getFirebaseDb(), 'payments', paymentId));
      if (paymentDoc.exists()) {
        const payment = paymentDoc.data() as PaymentData;
        await this.sendPaymentNotifications(payment);
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }
  }

  // Send payment-related notifications
  static async sendPaymentNotifications(payment: PaymentData): Promise<void> {
    try {
      switch (payment.status) {
        case 'succeeded':
          // Notify lawyer of payment received
          await NotificationService.notifyPaymentReceived(
            payment.lawyerId,
            payment.metadata?.clientName || 'Cliente',
            {
              amount: payment.amount,
              service: payment.description
            }
          );
          break;

        case 'failed':
          // Notify client of payment failure
          await NotificationService.notifyPaymentFailed(
            payment.clientId,
            {
              amount: payment.amount,
              failureReason: payment.metadata?.failureReason || 'Error en el procesamiento'
            }
          );
          break;

        case 'pending':
          // Notify client of pending payment
          await NotificationService.notifyPaymentPending(
            payment.clientId,
            {
              amount: payment.amount,
              service: payment.description,
              dueDate: payment.metadata?.dueDate
            }
          );
          break;
      }
    } catch (error) {
      console.error('Error sending payment notifications:', error);
    }
  }

  // Calculate platform fee (e.g., 5% platform fee)
  static calculatePlatformFee(amount: number, feePercentage: number = 0.05): number {
    return Math.round(amount * feePercentage * 100) / 100;
  }

  // Calculate lawyer's net amount after platform fee
  static calculateLawyerAmount(amount: number, platformFee: number): number {
    return Math.round((amount - platformFee) * 100) / 100;
  }

  // Create payment for appointment
  static async createAppointmentPayment(appointmentPayment: AppointmentPayment): Promise<string> {
    const platformFee = this.calculatePlatformFee(appointmentPayment.amount);
    const lawyerAmount = this.calculateLawyerAmount(appointmentPayment.amount, platformFee);

    const paymentData: Omit<PaymentData, 'id' | 'createdAt' | 'updatedAt'> = {
      clientId: appointmentPayment.clientId,
      lawyerId: appointmentPayment.lawyerId,
      appointmentId: appointmentPayment.appointmentId,
      amount: appointmentPayment.amount,
      currency: 'usd',
      description: appointmentPayment.description,
      status: 'pending',
      platformFee,
      lawyerAmount,
      metadata: {
        appointmentDate: appointmentPayment.appointmentDate,
        appointmentTime: appointmentPayment.appointmentTime,
        specialty: appointmentPayment.specialty,
        lawyerName: appointmentPayment.lawyerName,
        clientName: appointmentPayment.clientName
      }
    };

    return await this.createPayment(paymentData);
  }

  // Get payment by ID
  static async getPayment(paymentId: string): Promise<PaymentData | null> {
    try {
      const paymentDoc = await getDoc(doc(getFirebaseDb(), 'payments', paymentId));
      if (paymentDoc.exists()) {
        return { id: paymentDoc.id, ...paymentDoc.data() } as PaymentData;
      }
      return null;
    } catch (error) {
      console.error('Error getting payment:', error);
      return null;
    }
  }

  // Get payments for a user (client or lawyer)
  static async getUserPayments(userId: string, userType: 'client' | 'lawyer'): Promise<PaymentData[]> {
    try {
      const field = userType === 'client' ? 'clientId' : 'lawyerId';
      const paymentsQuery = query(
        collection(getFirebaseDb(), 'payments'),
        where(field, '==', userId)
      );

      const paymentsSnapshot = await getDocs(paymentsQuery);
      return paymentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as PaymentData[];
    } catch (error) {
      console.error('Error getting user payments:', error);
      return [];
    }
  }

  // Get lawyer earnings summary
  static async getLawyerEarnings(lawyerId: string, startDate?: Date, endDate?: Date): Promise<{
    totalEarnings: number;
    totalPayments: number;
    pendingAmount: number;
    platformFees: number;
  }> {
    try {
      let paymentsQuery = query(
        collection(getFirebaseDb(), 'payments'),
        where('lawyerId', '==', lawyerId)
      );

      const paymentsSnapshot = await getDocs(paymentsQuery);
      const payments = paymentsSnapshot.docs.map(doc => doc.data()) as PaymentData[];

      // Filter by date range if provided
      const filteredPayments = payments.filter(payment => {
        if (!startDate || !endDate) return true;
        const paymentDate = new Date(payment.createdAt);
        return paymentDate >= startDate && paymentDate <= endDate;
      });

      const succeededPayments = filteredPayments.filter(p => p.status === 'succeeded');
      const pendingPayments = filteredPayments.filter(p => p.status === 'pending');

      return {
        totalEarnings: succeededPayments.reduce((sum, p) => sum + p.lawyerAmount, 0),
        totalPayments: succeededPayments.length,
        pendingAmount: pendingPayments.reduce((sum, p) => sum + p.lawyerAmount, 0),
        platformFees: succeededPayments.reduce((sum, p) => sum + p.platformFee, 0)
      };
    } catch (error) {
      console.error('Error getting lawyer earnings:', error);
      return {
        totalEarnings: 0,
        totalPayments: 0,
        pendingAmount: 0,
        platformFees: 0
      };
    }
  }

  // Process refund
  static async processRefund(
    paymentId: string, 
    amount?: number, 
    reason?: string
  ): Promise<boolean> {
    try {
      const payment = await this.getPayment(paymentId);
      if (!payment || payment.status !== 'succeeded') {
        throw new Error('Payment not found or not eligible for refund');
      }

      // Update payment status to refunded
      await this.updatePaymentStatus(paymentId, 'refunded', {
        refundId: `refund_${Date.now()}`, // This would be the actual Stripe refund ID
        metadata: {
          ...payment.metadata,
          refundReason: reason,
          refundAmount: amount || payment.amount
        }
      });

      return true;
    } catch (error) {
      console.error('Error processing refund:', error);
      return false;
    }
  }

  // Get payment statistics for admin dashboard
  static async getPaymentStatistics(startDate?: Date, endDate?: Date): Promise<{
    totalRevenue: number;
    totalTransactions: number;
    platformRevenue: number;
    successRate: number;
    averageTransactionAmount: number;
  }> {
    try {
      const paymentsSnapshot = await getDocs(collection(getFirebaseDb(), 'payments'));
      const payments = paymentsSnapshot.docs.map(doc => doc.data()) as PaymentData[];

      // Filter by date range if provided
      const filteredPayments = payments.filter(payment => {
        if (!startDate || !endDate) return true;
        const paymentDate = new Date(payment.createdAt);
        return paymentDate >= startDate && paymentDate <= endDate;
      });

      const succeededPayments = filteredPayments.filter(p => p.status === 'succeeded');
      const totalTransactions = filteredPayments.length;

      return {
        totalRevenue: succeededPayments.reduce((sum, p) => sum + p.amount, 0),
        totalTransactions,
        platformRevenue: succeededPayments.reduce((sum, p) => sum + p.platformFee, 0),
        successRate: totalTransactions > 0 ? (succeededPayments.length / totalTransactions) * 100 : 0,
        averageTransactionAmount: succeededPayments.length > 0 
          ? succeededPayments.reduce((sum, p) => sum + p.amount, 0) / succeededPayments.length 
          : 0
      };
    } catch (error) {
      console.error('Error getting payment statistics:', error);
      return {
        totalRevenue: 0,
        totalTransactions: 0,
        platformRevenue: 0,
        successRate: 0,
        averageTransactionAmount: 0
      };
    }
  }
}
