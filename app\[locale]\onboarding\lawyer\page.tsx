import { Metadata } from 'next';
import { setRequestLocale } from 'next-intl/server';
import { LawyerOnboarding } from '@/components/onboarding/lawyer-onboarding';

// Force dynamic rendering to prevent Firebase initialization during build
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Configuración de Cuenta - DelawPR',
  description: 'Completa la configuración de tu cuenta profesional en DelawPR',
};

interface LawyerOnboardingPageProps {
  params: { locale: string };
}

export default function LawyerOnboardingPage({ params: { locale } }: LawyerOnboardingPageProps) {
  // Enable static rendering
  setRequestLocale(locale);

  return <LawyerOnboarding />;
}

export function generateStaticParams() {
  return [{ locale: 'en' }, { locale: 'es' }];
}
