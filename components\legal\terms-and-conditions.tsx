'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  FileText, 
  Shield, 
  DollarSign, 
  Users, 
  AlertTriangle,
  CheckCircle,
  Scale,
  Clock
} from 'lucide-react';

interface TermsAndConditionsProps {
  onAccept: () => void;
  onDecline: () => void;
  userType: 'lawyer' | 'client';
  loading?: boolean;
}

export function TermsAndConditions({ 
  onAccept, 
  onDecline, 
  userType, 
  loading = false 
}: TermsAndConditionsProps) {
  const [hasReadTerms, setHasReadTerms] = useState(false);
  const [hasReadPrivacy, setHasReadPrivacy] = useState(false);
  const [hasReadSubscription, setHasReadSubscription] = useState(false);

  const canAccept = hasReadTerms && hasReadPrivacy && (userType === 'client' || hasReadSubscription);

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center p-4">
      <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <CardHeader className="p-6 md:p-8 border-b border-white/10">
          <CardTitle className="text-white text-2xl md:text-3xl flex items-center">
            <Scale className="mr-3 h-8 w-8 text-yellow-400" />
            Términos y Condiciones
          </CardTitle>
          <p className="text-gray-300 mt-2">
            {userType === 'lawyer' 
              ? 'Términos de servicio para abogados profesionales'
              : 'Términos de uso para clientes'
            }
          </p>
        </CardHeader>
        
        <CardContent className="p-0">
          <div className="max-h-[60vh] overflow-y-auto p-6 md:p-8 space-y-8">
            
            {/* Terms of Service */}
            <section className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-xl flex items-center justify-center">
                  <FileText className="h-5 w-5 text-blue-400" />
                </div>
                <h3 className="text-xl font-bold text-white">1. Términos de Servicio</h3>
              </div>
              
              <div className="bg-white/5 rounded-2xl p-6 space-y-4 text-gray-300 text-sm leading-relaxed">
                <p>
                  <strong className="text-white">1.1 Aceptación de Términos:</strong> Al usar DelawPR, usted acepta estar sujeto a estos términos y condiciones.
                </p>
                <p>
                  <strong className="text-white">1.2 Elegibilidad:</strong> {userType === 'lawyer' 
                    ? 'Debe ser un abogado licenciado en Puerto Rico con credenciales válidas.'
                    : 'Debe ser mayor de 18 años para usar nuestros servicios.'
                  }
                </p>
                <p>
                  <strong className="text-white">1.3 Uso Apropiado:</strong> Se compromete a usar la plataforma de manera profesional y ética, cumpliendo con todas las leyes aplicables.
                </p>
                {userType === 'lawyer' && (
                  <>
                    <p>
                      <strong className="text-white">1.4 Responsabilidad Profesional:</strong> Mantiene toda la responsabilidad profesional y ética hacia sus clientes según las normas del Colegio de Abogados de Puerto Rico.
                    </p>
                    <p>
                      <strong className="text-white">1.5 Información Veraz:</strong> Garantiza que toda la información profesional proporcionada es precisa y actualizada.
                    </p>
                  </>
                )}
              </div>
              
              <label className="flex items-center space-x-3 cursor-pointer">
                <Checkbox
                  checked={hasReadTerms}
                  onCheckedChange={(checked) => setHasReadTerms(checked === true)}
                  className="border-yellow-500 data-[state=checked]:bg-yellow-500"
                />
                <span className="text-white text-sm">
                  He leído y acepto los términos de servicio
                </span>
              </label>
            </section>

            {/* Privacy Policy */}
            <section className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-xl flex items-center justify-center">
                  <Shield className="h-5 w-5 text-green-400" />
                </div>
                <h3 className="text-xl font-bold text-white">2. Política de Privacidad</h3>
              </div>
              
              <div className="bg-white/5 rounded-2xl p-6 space-y-4 text-gray-300 text-sm leading-relaxed">
                <p>
                  <strong className="text-white">2.1 Recopilación de Datos:</strong> Recopilamos información necesaria para proporcionar nuestros servicios legales.
                </p>
                <p>
                  <strong className="text-white">2.2 Uso de Información:</strong> Su información se usa únicamente para facilitar servicios legales y mejorar la plataforma.
                </p>
                <p>
                  <strong className="text-white">2.3 Protección de Datos:</strong> Implementamos medidas de seguridad robustas para proteger su información personal.
                </p>
                <p>
                  <strong className="text-white">2.4 Confidencialidad:</strong> {userType === 'lawyer' 
                    ? 'Respetamos el privilegio abogado-cliente y mantenemos la confidencialidad de todas las comunicaciones.'
                    : 'Toda comunicación con abogados está protegida por confidencialidad profesional.'
                  }
                </p>
                <p>
                  <strong className="text-white">2.5 Compartir Información:</strong> No vendemos ni compartimos su información personal con terceros sin su consentimiento.
                </p>
              </div>
              
              <label className="flex items-center space-x-3 cursor-pointer">
                <Checkbox
                  checked={hasReadPrivacy}
                  onCheckedChange={(checked) => setHasReadPrivacy(checked === true)}
                  className="border-yellow-500 data-[state=checked]:bg-yellow-500"
                />
                <span className="text-white text-sm">
                  He leído y acepto la política de privacidad
                </span>
              </label>
            </section>

            {/* Subscription Terms (Lawyers Only) */}
            {userType === 'lawyer' && (
              <section className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-xl flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-yellow-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">3. Términos de Suscripción</h3>
                </div>
                
                <div className="bg-gradient-to-r from-yellow-500/10 to-amber-500/10 border border-yellow-500/20 rounded-2xl p-6 space-y-4">
                  <div className="flex items-center space-x-3 mb-4">
                    <AlertTriangle className="h-6 w-6 text-yellow-400" />
                    <h4 className="text-lg font-bold text-yellow-400">Suscripción Mensual Requerida</h4>
                  </div>
                  
                  <div className="space-y-4 text-gray-300 text-sm leading-relaxed">
                    <p>
                      <strong className="text-white">3.1 Tarifa Mensual:</strong> $99.99/mes para acceso completo a la plataforma y visibilidad para clientes.
                    </p>
                    <p>
                      <strong className="text-white">3.2 Facturación:</strong> Se factura mensualmente el mismo día de cada mes desde su registro.
                    </p>
                    <p>
                      <strong className="text-white">3.3 Servicios Incluidos:</strong>
                    </p>
                    <ul className="list-disc list-inside ml-4 space-y-2">
                      <li>Perfil profesional visible para clientes</li>
                      <li>Sistema de citas y calendario</li>
                      <li>Mensajería segura con clientes</li>
                      <li>Procesamiento de pagos integrado</li>
                      <li>Reportes y análisis de negocio</li>
                      <li>Soporte técnico prioritario</li>
                    </ul>
                    <p>
                      <strong className="text-white">3.4 Suspensión por Falta de Pago:</strong> Su perfil será ocultado de los clientes si el pago mensual no se procesa exitosamente.
                    </p>
                    <p>
                      <strong className="text-white">3.5 Cancelación:</strong> Puede cancelar en cualquier momento. El acceso continúa hasta el final del período facturado.
                    </p>
                    <p>
                      <strong className="text-white">3.6 Reembolsos:</strong> No se ofrecen reembolsos por períodos parciales, excepto según lo requerido por la ley.
                    </p>
                  </div>
                </div>
                
                <label className="flex items-center space-x-3 cursor-pointer">
                  <Checkbox
                    checked={hasReadSubscription}
                    onCheckedChange={(checked) => setHasReadSubscription(checked === true)}
                    className="border-yellow-500 data-[state=checked]:bg-yellow-500"
                  />
                  <span className="text-white text-sm">
                    He leído y acepto los términos de suscripción mensual
                  </span>
                </label>
              </section>
            )}

            {/* Additional Terms */}
            <section className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-xl flex items-center justify-center">
                  <Users className="h-5 w-5 text-purple-400" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  {userType === 'lawyer' ? '4' : '3'}. Términos Adicionales
                </h3>
              </div>
              
              <div className="bg-white/5 rounded-2xl p-6 space-y-4 text-gray-300 text-sm leading-relaxed">
                <p>
                  <strong className="text-white">Limitación de Responsabilidad:</strong> DelawPR actúa como plataforma de conexión. No somos responsables por la calidad de los servicios legales proporcionados.
                </p>
                <p>
                  <strong className="text-white">Modificaciones:</strong> Nos reservamos el derecho de modificar estos términos con notificación previa de 30 días.
                </p>
                <p>
                  <strong className="text-white">Ley Aplicable:</strong> Estos términos se rigen por las leyes del Estado Libre Asociado de Puerto Rico.
                </p>
                <p>
                  <strong className="text-white">Resolución de Disputas:</strong> Las disputas se resolverán mediante arbitraje en San Juan, Puerto Rico.
                </p>
              </div>
            </section>
          </div>
          
          {/* Action Buttons */}
          <div className="border-t border-white/10 p-6 md:p-8">
            <div className="flex flex-col md:flex-row gap-4">
              <Button
                onClick={onDecline}
                disabled={loading}
                className="flex-1 bg-gray-500/20 border border-gray-500/30 text-gray-400 hover:bg-gray-500/30 hover:border-gray-500/50 py-3 rounded-2xl"
              >
                Rechazar
              </Button>
              
              <Button
                onClick={onAccept}
                disabled={!canAccept || loading}
                className="flex-1 bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold py-3 rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                    Procesando...
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Aceptar y Continuar
                  </>
                )}
              </Button>
            </div>
            
            {!canAccept && (
              <p className="text-yellow-400 text-sm mt-4 text-center">
                Debe aceptar todos los términos para continuar
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
