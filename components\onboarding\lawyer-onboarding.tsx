'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { TermsAndConditions } from '@/components/legal/terms-and-conditions';
import { SubscriptionPayment } from '@/components/subscription/subscription-payment';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  Clock, 
  Star, 
  DollarSign,
  Users,
  Calendar,
  MessageSquare,
  BarChart3,
  Shield,
  Zap
} from 'lucide-react';
import { SubscriptionService } from '@/services/subscription-service';

type OnboardingStep = 'terms' | 'plan-selection' | 'payment' | 'complete';

export function LawyerOnboarding() {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('terms');
  const [selectedPlan, setSelectedPlan] = useState(SubscriptionService.PLANS[0]);
  const [loading, setLoading] = useState(false);
  const [hasAcceptedTerms, setHasAcceptedTerms] = useState(false);

  const checkOnboardingStatus = useCallback(async () => {
    if (!user) return;

    try {
      // Check if terms are already accepted
      const termsAccepted = await SubscriptionService.hasAcceptedTerms(user.id);
      setHasAcceptedTerms(termsAccepted);

      // Check if subscription exists
      const hasActiveSubscription = await SubscriptionService.hasActiveSubscription(user.id);

      if (termsAccepted && hasActiveSubscription) {
        setCurrentStep('complete');
      } else if (termsAccepted) {
        setCurrentStep('plan-selection');
      }
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  }, [user]);

  useEffect(() => {
    checkOnboardingStatus();
  }, [checkOnboardingStatus]);

  const handleAcceptTerms = async () => {
    try {
      setLoading(true);
      await SubscriptionService.acceptTerms(user.id);
      setHasAcceptedTerms(true);
      setCurrentStep('plan-selection');
    } catch (error) {
      console.error('Error accepting terms:', error);
      alert('Error al aceptar los términos. Intenta de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeclineTerms = () => {
    // Redirect to home or show decline message
    window.location.href = '/es';
  };

  const handlePlanSelect = (plan: any) => {
    setSelectedPlan(plan);
    setCurrentStep('payment');
  };

  const handlePaymentSuccess = () => {
    setCurrentStep('complete');
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    alert(`Error en el pago: ${error}`);
  };

  const renderStepIndicator = () => {
    const steps = [
      { id: 'terms', label: 'Términos', icon: Shield },
      { id: 'plan-selection', label: 'Plan', icon: Star },
      { id: 'payment', label: 'Pago', icon: DollarSign },
      { id: 'complete', label: 'Completo', icon: CheckCircle }
    ];

    const stepIndex = steps.findIndex(step => step.id === currentStep);

    return (
      <div className="flex items-center justify-center space-x-4 mb-8">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === stepIndex;
          const isCompleted = index < stepIndex;
          
          return (
            <div key={step.id} className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                isCompleted 
                  ? 'bg-green-500 text-white' 
                  : isActive 
                    ? 'bg-yellow-500 text-black' 
                    : 'bg-gray-600 text-gray-400'
              }`}>
                <Icon className="h-5 w-5" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                isActive ? 'text-yellow-400' : isCompleted ? 'text-green-400' : 'text-gray-400'
              }`}>
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <div className={`w-8 h-0.5 mx-4 ${
                  isCompleted ? 'bg-green-500' : 'bg-gray-600'
                }`} />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  if (currentStep === 'terms') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
        {renderStepIndicator()}
        <TermsAndConditions
          userType="lawyer"
          onAccept={handleAcceptTerms}
          onDecline={handleDeclineTerms}
          loading={loading}
        />
      </div>
    );
  }

  if (currentStep === 'plan-selection') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black p-4">
        {renderStepIndicator()}
        
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Elige tu Plan de Suscripción
            </h1>
            <p className="text-gray-300 text-lg">
              Selecciona el plan que mejor se adapte a tu práctica legal
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {SubscriptionService.PLANS.map((plan) => {
              const isYearly = plan.interval === 'year';
              const monthlyCost = 99.99;
              const savings = isYearly ? (monthlyCost * 12) - plan.price : 0;
              
              return (
                <Card key={plan.id} className={`bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border rounded-3xl transition-all duration-300 hover:scale-105 cursor-pointer ${
                  isYearly 
                    ? 'border-yellow-500/50 shadow-yellow-500/20 shadow-lg' 
                    : 'border-white/20 hover:border-yellow-500/30'
                }`}>
                  <CardHeader className="p-6 text-center">
                    {isYearly && (
                      <Badge className="bg-gradient-to-r from-green-400/20 to-green-500/20 border border-green-400/30 text-green-400 mb-4 mx-auto w-fit">
                        Más Popular - Ahorra ${savings.toFixed(0)}
                      </Badge>
                    )}
                    <CardTitle className="text-white text-2xl mb-2">{plan.name}</CardTitle>
                    <div className="text-4xl font-bold text-yellow-400 mb-2">
                      ${plan.price.toFixed(2)}
                    </div>
                    <p className="text-gray-400">
                      por {plan.interval === 'month' ? 'mes' : 'año'}
                    </p>
                    {isYearly && (
                      <p className="text-green-400 text-sm mt-2">
                        Equivale a ${(plan.price / 12).toFixed(2)}/mes
                      </p>
                    )}
                  </CardHeader>
                  
                  <CardContent className="p-6">
                    <div className="space-y-4 mb-6">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <Button
                      onClick={() => handlePlanSelect(plan)}
                      className={`w-full py-3 rounded-2xl font-bold transition-all duration-300 ${
                        isYearly
                          ? 'bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black'
                          : 'bg-gradient-to-r from-white/10 to-white/20 border border-white/30 text-white hover:bg-white/20 hover:border-white/40'
                      }`}
                    >
                      {isYearly ? 'Seleccionar Plan Anual' : 'Seleccionar Plan Mensual'}
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  if (currentStep === 'payment') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black p-4">
        {renderStepIndicator()}
        
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Activar Suscripción
            </h1>
            <p className="text-gray-300 text-lg">
              Completa tu pago para activar tu perfil profesional
            </p>
          </div>

          <SubscriptionPayment
            plan={selectedPlan}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
            onCancel={() => setCurrentStep('plan-selection')}
          />
        </div>
      </div>
    );
  }

  if (currentStep === 'complete') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black p-4 flex items-center justify-center">
        <Card className="bg-gradient-to-br from-black/80 via-black/60 to-black/80 backdrop-blur-2xl border border-green-500/20 rounded-3xl max-w-md w-full">
          <CardContent className="p-8 text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-10 w-10 text-green-400" />
            </div>
            
            <h2 className="text-3xl font-bold text-white mb-4">
              ¡Bienvenido a DelawPR!
            </h2>
            
            <p className="text-gray-300 mb-6">
              Tu cuenta profesional ha sido activada exitosamente. 
              Tu perfil ya está visible para los clientes.
            </p>
            
            <div className="bg-green-500/10 border border-green-500/20 rounded-2xl p-4 mb-6">
              <h3 className="text-green-400 font-semibold mb-2">Próximos pasos:</h3>
              <ul className="text-green-300 text-sm space-y-1 text-left">
                <li>• Completa tu perfil profesional</li>
                <li>• Configura tu disponibilidad</li>
                <li>• Revisa tu dashboard</li>
                <li>• ¡Comienza a recibir clientes!</li>
              </ul>
            </div>
            
            <Button
              onClick={() => window.location.href = '/es/dashboard/lawyer'}
              className="w-full bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:via-green-600 hover:to-green-700 text-black font-bold py-3 rounded-2xl"
            >
              Ir a mi Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
}
