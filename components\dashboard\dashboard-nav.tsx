'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { 
  Home,
  Calendar, 
  MessageSquare, 
  User,
  LogOut,
  Scale,
  Menu,
  X
} from 'lucide-react';
import Link from 'next/link';

interface DashboardNavProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export function DashboardNav({ activeSection, onSectionChange }: DashboardNavProps) {
  const { user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'citas', label: 'Citas', icon: Calendar },
    { id: 'mensajes', label: '<PERSON><PERSON><PERSON><PERSON>', icon: MessageSquare },
    { id: 'perfil', label: 'Perfil', icon: User },
  ];

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:bg-white/5 lg:backdrop-blur-xl lg:border-r lg:border-white/10">
        <div className="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4 mb-8">
            <div className="relative">
              <Scale className="h-8 w-8 text-yellow-500" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-black">PR</span>
              </div>
            </div>
            <div className="ml-3">
              <h1 className="text-xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 bg-clip-text text-transparent">
                Abogo
              </h1>
              <p className="text-xs text-gray-400 -mt-1">Cliente</p>
            </div>
          </div>

          {/* User Info */}
          <div className="px-4 mb-6">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-yellow-500/20 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-yellow-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-400">{user?.email}</p>
              </div>
            </div>
          </div>

          {/* Navigation Items */}
          <nav className="mt-5 flex-1 px-2 space-y-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeSection === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => onSectionChange(item.id)}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full transition-all duration-200 ${
                    isActive
                      ? 'bg-yellow-500/20 text-yellow-500 border-r-2 border-yellow-500'
                      : 'text-gray-300 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <Icon
                    className={`mr-3 flex-shrink-0 h-5 w-5 ${
                      isActive ? 'text-yellow-500' : 'text-gray-400 group-hover:text-gray-300'
                    }`}
                  />
                  {item.label}
                </button>
              );
            })}
          </nav>

          {/* Logout Button */}
          <div className="flex-shrink-0 px-2 pb-4">
            <Button
              onClick={handleLogout}
              variant="outline"
              className="w-full bg-white/10 border-white/20 text-white hover:bg-red-500/20 hover:border-red-500/40 hover:text-red-400 transition-all duration-200"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Cerrar Sesión
            </Button>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        <div className="bg-white/5 backdrop-blur-xl border-b border-white/10 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center">
            <div className="relative">
              <Scale className="h-6 w-6 text-yellow-500" />
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
            </div>
            <div className="ml-2">
              <h1 className="text-lg font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 bg-clip-text text-transparent">
                Abogo
              </h1>
            </div>
          </div>
          
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-white hover:text-yellow-500 transition-colors"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="bg-white/5 backdrop-blur-xl border-b border-white/10">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeSection === item.id;
                
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      onSectionChange(item.id);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`group flex items-center px-3 py-2 text-base font-medium rounded-md w-full transition-all duration-200 ${
                      isActive
                        ? 'bg-yellow-500/20 text-yellow-500'
                        : 'text-gray-300 hover:bg-white/10 hover:text-white'
                    }`}
                  >
                    <Icon
                      className={`mr-3 flex-shrink-0 h-5 w-5 ${
                        isActive ? 'text-yellow-500' : 'text-gray-400 group-hover:text-gray-300'
                      }`}
                    />
                    {item.label}
                  </button>
                );
              })}
              
              {/* Mobile Logout */}
              <button
                onClick={handleLogout}
                className="group flex items-center px-3 py-2 text-base font-medium rounded-md w-full text-gray-300 hover:bg-red-500/20 hover:text-red-400 transition-all duration-200"
              >
                <LogOut className="mr-3 flex-shrink-0 h-5 w-5 text-gray-400 group-hover:text-red-400" />
                Cerrar Sesión
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
