'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { 
  Home,
  Calendar, 
  MessageSquare, 
  User,
  LogOut,
  Scale,
  Menu,
  X
} from 'lucide-react';
import Link from 'next/link';

interface DashboardNavProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export function DashboardNav({ activeSection, onSectionChange }: DashboardNavProps) {
  const { user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'citas', label: 'Citas', icon: Calendar },
    { id: 'mensajes', label: '<PERSON><PERSON><PERSON><PERSON>', icon: MessageSquare },
    { id: 'perfil', label: 'Perfil', icon: User },
  ];

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex lg:flex-col lg:w-72 lg:fixed lg:inset-y-0 lg:bg-gradient-to-b lg:from-black/80 lg:via-black/60 lg:to-black/80 lg:backdrop-blur-2xl lg:border-r lg:border-yellow-500/20">
        <div className="flex flex-col flex-grow pt-8 pb-6 overflow-y-auto">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-6 mb-10">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                <Scale className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-black">PR</span>
              </div>
            </div>
            <div className="ml-4">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 bg-clip-text text-transparent">
                Abogo
              </h1>
              <p className="text-sm text-gray-400 -mt-1 font-medium">Dashboard Cliente</p>
            </div>
          </div>

          {/* User Info */}
          <div className="px-6 mb-8">
            <div className="bg-gradient-to-r from-black/40 to-black/60 backdrop-blur-xl border border-yellow-500/20 rounded-2xl p-6">
              <div className="flex items-center">
                <div className="w-14 h-14 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                  <User className="h-7 w-7 text-yellow-400" />
                </div>
                <div className="ml-4">
                  <p className="text-base font-bold text-white">
                    {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-sm text-gray-400">{user?.email}</p>
                  <div className="flex items-center mt-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    <span className="text-xs text-green-400 font-medium">En línea</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Items */}
          <nav className="flex-1 px-6 space-y-3">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeSection === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => onSectionChange(item.id)}
                  className={`group flex items-center px-6 py-4 text-base font-semibold rounded-2xl w-full transition-all duration-300 ${
                    isActive
                      ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400 shadow-lg shadow-yellow-500/10'
                      : 'text-gray-300 hover:bg-white/10 hover:text-white hover:border hover:border-white/20 border border-transparent'
                  }`}
                >
                  <div className={`w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 ${
                    isActive
                      ? 'bg-gradient-to-br from-yellow-400/30 to-amber-500/30'
                      : 'bg-white/10 group-hover:bg-white/20'
                  }`}>
                    <Icon
                      className={`h-5 w-5 ${
                        isActive ? 'text-yellow-400' : 'text-gray-400 group-hover:text-white'
                      }`}
                    />
                  </div>
                  {item.label}
                </button>
              );
            })}
          </nav>

          {/* Logout Button */}
          <div className="flex-shrink-0 px-6 pb-6">
            <Button
              onClick={handleLogout}
              className="w-full bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30 text-red-400 hover:bg-red-500/30 hover:border-red-500/50 hover:text-red-300 font-semibold py-4 rounded-2xl transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10"
            >
              <LogOut className="mr-3 h-5 w-5" />
              Cerrar Sesión
            </Button>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        <div className="bg-gradient-to-r from-black/80 to-black/60 backdrop-blur-2xl border-b border-yellow-500/20 px-6 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                <Scale className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full"></div>
            </div>
            <div className="ml-3">
              <h1 className="text-xl font-bold bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 bg-clip-text text-transparent">
                Abogo
              </h1>
            </div>
          </div>

          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="w-10 h-10 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl flex items-center justify-center text-white hover:text-yellow-400 hover:border-yellow-400/30 transition-all duration-300"
          >
            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="bg-gradient-to-b from-black/80 to-black/60 backdrop-blur-2xl border-b border-yellow-500/20">
            <div className="px-4 pt-4 pb-6 space-y-3">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeSection === item.id;

                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      onSectionChange(item.id);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`group flex items-center px-4 py-3 text-base font-semibold rounded-2xl w-full transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400'
                        : 'text-gray-300 hover:bg-white/10 hover:text-white border border-transparent hover:border-white/20'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-xl flex items-center justify-center mr-3 ${
                      isActive
                        ? 'bg-gradient-to-br from-yellow-400/30 to-amber-500/30'
                        : 'bg-white/10 group-hover:bg-white/20'
                    }`}>
                      <Icon
                        className={`h-4 w-4 ${
                          isActive ? 'text-yellow-400' : 'text-gray-400 group-hover:text-white'
                        }`}
                      />
                    </div>
                    {item.label}
                  </button>
                );
              })}

              {/* Mobile Logout */}
              <button
                onClick={handleLogout}
                className="group flex items-center px-4 py-3 text-base font-semibold rounded-2xl w-full bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30 text-red-400 hover:bg-red-500/30 hover:border-red-500/50 transition-all duration-300"
              >
                <div className="w-8 h-8 rounded-xl flex items-center justify-center mr-3 bg-red-500/20">
                  <LogOut className="h-4 w-4 text-red-400" />
                </div>
                Cerrar Sesión
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
