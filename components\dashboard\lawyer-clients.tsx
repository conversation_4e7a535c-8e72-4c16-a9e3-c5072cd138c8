'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Search,
  Mail,
  Phone,
  MapPin,
  Calendar,
  MessageSquare,
  Star,
  Eye,
  Filter,
  Plus,
  User,
  CheckCircle,
  X
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { getFirebaseDb } from '@/lib/firebase';

export function LawyerClients() {
  const { user } = useAuth();
  const [clients, setClients] = useState<any[]>([]);
  const [filteredClients, setFilteredClients] = useState<any[]>([]);
  const [serviceRequests, setServiceRequests] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('clients'); // 'clients' or 'requests'



  useEffect(() => {
    // Filter clients based on search term
    const filtered = clients.filter(client =>
      client.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredClients(filtered);
  }, [clients, searchTerm]);

  const loadClients = useCallback(async () => {
    try {
      setLoading(true);

      // Load service requests
      const requestsQuery = query(
        collection(getFirebaseDb(), 'serviceRequests'),
        where('lawyerId', '==', user!.id),
        where('status', 'in', ['pending', 'approved', 'rejected']),
        orderBy('createdAt', 'desc')
      );

      const requestsSnapshot = await getDocs(requestsQuery);
      const requestsData = requestsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setServiceRequests(requestsData);

      // Load appointments to get client IDs
      const appointmentsQuery = query(
        collection(getFirebaseDb(), 'appointments'),
        where('lawyerId', '==', user!.id),
        orderBy('date', 'desc')
      );

      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      const clientIds = new Set();
      const clientData: any = {};

      appointmentsSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.clientId) {
          clientIds.add(data.clientId);
          if (!clientData[data.clientId]) {
            clientData[data.clientId] = {
              id: data.clientId,
              firstName: data.clientName?.split(' ')[0] || 'Cliente',
              lastName: data.clientName?.split(' ').slice(1).join(' ') || '',
              email: data.clientEmail || '',
              phone: data.clientPhone || '',
              totalAppointments: 0,
              lastAppointment: data.date,
              status: data.status || 'active'
            };
          }
          clientData[data.clientId].totalAppointments++;
        }
      });

      setClients(Object.values(clientData));
    } catch (error) {
      console.error('Error loading clients:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadClients();
    }
  }, [user, loadClients]);

  const handleApproveRequest = async (requestId: string) => {
    try {
      const { updateDoc, doc } = await import('firebase/firestore');
      await updateDoc(doc(getFirebaseDb(), 'serviceRequests', requestId), {
        status: 'approved',
        approvedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      // Send notification to client
      const request = serviceRequests.find(r => r.id === requestId);
      if (request) {
        const { NotificationService } = await import('@/services/notification-service');
        await NotificationService.notifyLawyerResponse(
          request.clientId,
          `${user!.firstName} ${user!.lastName}`,
          { inquiryId: requestId }
        );
      }

      // Reload data
      loadClients();
    } catch (error) {
      console.error('Error approving request:', error);
    }
  };

  const handleRejectRequest = async (requestId: string) => {
    try {
      const { updateDoc, doc } = await import('firebase/firestore');
      await updateDoc(doc(getFirebaseDb(), 'serviceRequests', requestId), {
        status: 'rejected',
        rejectedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      // Reload data
      loadClients();
    } catch (error) {
      console.error('Error rejecting request:', error);
    }
  };

  const pendingRequests = serviceRequests.filter(r => r.status === 'pending');

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent flex items-center">
              <Users className="mr-3 h-8 w-8 text-blue-400" />
              Mis Clientes
            </h1>
            <p className="mt-3 text-gray-300 text-lg">
              Gestiona tu cartera de clientes y su historial de consultas
            </p>
          </div>
          <Button className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-semibold px-6 py-3 rounded-2xl">
            <Plus className="mr-2 h-5 w-5" />
            Agregar Cliente
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab('clients')}
            className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 ${
              activeTab === 'clients'
                ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400'
                : 'bg-black/40 backdrop-blur-xl border border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
            }`}
          >
            Mis Clientes ({clients.length})
          </button>
          <button
            onClick={() => setActiveTab('requests')}
            className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 relative ${
              activeTab === 'requests'
                ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400'
                : 'bg-black/40 backdrop-blur-xl border border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
            }`}
          >
            Solicitudes de Servicio ({pendingRequests.length})
            {pendingRequests.length > 0 && (
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-white">{pendingRequests.length}</span>
              </div>
            )}
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder={activeTab === 'clients' ? "Buscar clientes por nombre o email..." : "Buscar solicitudes..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 h-12 bg-black/40 backdrop-blur-xl border border-yellow-500/30 rounded-2xl text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50"
            />
          </div>
          <Button className="bg-black/40 backdrop-blur-xl border border-white/20 text-white hover:bg-white/10 hover:border-white/30 px-6 py-3 rounded-2xl">
            <Filter className="mr-2 h-5 w-5" />
            Filtros
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-blue-500/20 rounded-3xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-400 text-sm font-medium">Total Clientes</p>
                <p className="text-3xl font-bold text-white">{clients.length}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-2xl flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-green-500/20 rounded-3xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-400 text-sm font-medium">Clientes Activos</p>
                <p className="text-3xl font-bold text-white">{clients.filter(c => c.status === 'active').length}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-2xl flex items-center justify-center">
                <Users className="h-6 w-6 text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-yellow-500/20 rounded-3xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-400 text-sm font-medium">Nuevos Este Mes</p>
                <p className="text-3xl font-bold text-white">5</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400/20 to-amber-600/20 rounded-2xl flex items-center justify-center">
                <Plus className="h-6 w-6 text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Service Requests */}
      {activeTab === 'requests' && (
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl mb-8">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl">Solicitudes de Servicio</CardTitle>
            <CardDescription className="text-gray-400">
              Clientes que solicitan tus servicios legales
            </CardDescription>
          </CardHeader>
          <CardContent className="p-8">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
                <p className="text-gray-400 mt-4 text-lg">Cargando solicitudes...</p>
              </div>
            ) : pendingRequests.length > 0 ? (
              <div className="space-y-6">
                {pendingRequests.map((request) => (
                  <div key={request.id} className="group bg-gradient-to-r from-white/5 to-white/10 hover:from-yellow-500/10 hover:to-amber-500/10 border border-white/10 hover:border-yellow-500/30 rounded-2xl p-6 transition-all duration-300">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-orange-400/20 to-orange-600/20 rounded-2xl flex items-center justify-center">
                          <User className="h-8 w-8 text-orange-400" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-white mb-2">
                            {request.clientName}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                            <div className="flex items-center">
                              <Mail className="h-4 w-4 mr-1" />
                              {request.clientEmail}
                            </div>
                            {request.clientPhone && (
                              <div className="flex items-center">
                                <Phone className="h-4 w-4 mr-1" />
                                {request.clientPhone}
                              </div>
                            )}
                          </div>
                          <div className="mb-4">
                            <Badge className="bg-gradient-to-r from-blue-400/20 to-blue-500/20 border border-blue-400/30 text-blue-400 mb-2">
                              {request.specialty || 'Consulta General'}
                            </Badge>
                          </div>
                          <div className="bg-white/5 rounded-xl p-4 mb-4">
                            <h4 className="text-white font-semibold mb-2">Descripción del caso:</h4>
                            <p className="text-gray-300 text-sm">{request.message || 'Sin descripción adicional'}</p>
                          </div>
                          <div className="text-xs text-gray-500">
                            Solicitud enviada: {new Date(request.createdAt).toLocaleDateString('es-ES')}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col space-y-3">
                        <Button
                          onClick={() => handleApproveRequest(request.id)}
                          className="bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 hover:border-green-500/50 rounded-xl px-6 py-2"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Aprobar
                        </Button>
                        <Button
                          onClick={() => handleRejectRequest(request.id)}
                          className="bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30 text-red-400 hover:bg-red-500/30 hover:border-red-500/50 rounded-xl px-6 py-2"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Rechazar
                        </Button>
                        <Button className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 hover:border-blue-500/50 rounded-xl px-6 py-2">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Contactar
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
                  <Users className="h-10 w-10 text-gray-500" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">No hay solicitudes pendientes</h3>
                <p className="text-gray-400">
                  Las nuevas solicitudes de servicio aparecerán aquí
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Clients List */}
      {activeTab === 'clients' && (
        <Card className="bg-gradient-to-br from-black/60 via-black/40 to-black/60 backdrop-blur-2xl border border-white/10 rounded-3xl">
          <CardHeader className="p-8 border-b border-white/10">
            <CardTitle className="text-white text-xl">Lista de Clientes</CardTitle>
            <CardDescription className="text-gray-400">
              Información detallada de todos tus clientes
            </CardDescription>
          </CardHeader>
        <CardContent className="p-8">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
              <p className="text-gray-400 mt-4 text-lg">Cargando clientes...</p>
            </div>
          ) : filteredClients.length > 0 ? (
            <div className="space-y-6">
              {filteredClients.map((client) => (
                <div key={client.id} className="group bg-gradient-to-r from-white/5 to-white/10 hover:from-yellow-500/10 hover:to-amber-500/10 border border-white/10 hover:border-yellow-500/30 rounded-2xl p-6 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-2xl flex items-center justify-center">
                        <Users className="h-8 w-8 text-blue-400" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white mb-1">
                          {client.firstName} {client.lastName}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          {client.email && (
                            <div className="flex items-center">
                              <Mail className="h-4 w-4 mr-1" />
                              {client.email}
                            </div>
                          )}
                          {client.phone && (
                            <div className="flex items-center">
                              <Phone className="h-4 w-4 mr-1" />
                              {client.phone}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 mt-2">
                          <Badge className="bg-gradient-to-r from-green-400/20 to-green-500/20 border border-green-400/30 text-green-400">
                            {client.totalAppointments} citas
                          </Badge>
                          <span className="text-xs text-gray-500">
                            Última cita: {client.lastAppointment || 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <Button size="sm" className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 hover:border-blue-500/50 rounded-xl">
                        <Eye className="h-4 w-4 mr-1" />
                        Ver Perfil
                      </Button>
                      <Button size="sm" className="bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 hover:border-green-500/50 rounded-xl">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Mensaje
                      </Button>
                      <Button size="sm" className="bg-gradient-to-r from-yellow-400/20 to-amber-500/20 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/30 hover:border-yellow-400/50 rounded-xl">
                        <Calendar className="h-4 w-4 mr-1" />
                        Agendar
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <Users className="h-10 w-10 text-gray-500" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                {searchTerm ? 'No se encontraron clientes' : 'No tienes clientes aún'}
              </h3>
              <p className="text-gray-400 mb-6">
                {searchTerm 
                  ? 'Intenta con otros términos de búsqueda'
                  : 'Los clientes aparecerán aquí cuando agenden citas contigo'
                }
              </p>
              {!searchTerm && (
                <Button className="bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 hover:from-yellow-500 hover:via-amber-600 hover:to-yellow-700 text-black font-bold px-8 py-3 rounded-2xl">
                  <Plus className="mr-2 h-5 w-5" />
                  Agregar Primer Cliente
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      )}
    </div>
  );
}
