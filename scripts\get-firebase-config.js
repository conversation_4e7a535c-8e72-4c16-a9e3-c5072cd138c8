#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 Getting Firebase Configuration for delawpr project\n');

try {
  // Get the Firebase apps for the project
  console.log('📱 Fetching Firebase apps...');
  const appsResult = execSync('npx firebase apps:list --project delawpr', { encoding: 'utf8' });
  console.log(appsResult);
  
  // Check if there are any web apps
  if (appsResult.includes('No apps found')) {
    console.log('❌ No web apps found in the project.');
    console.log('📝 You need to create a web app first:');
    console.log('   1. Go to https://console.firebase.google.com/project/delawpr');
    console.log('   2. Click "Add app" → Web app');
    console.log('   3. Register your app with name "delawpr-web"');
    console.log('   4. Copy the configuration and update .env.local');
  } else {
    console.log('✅ Web apps found! You can get the configuration from:');
    console.log('   https://console.firebase.google.com/project/delawpr/settings/general');
    console.log('   Scroll down to "Your apps" section and copy the config object');
  }
  
} catch (error) {
  console.error('❌ Error getting Firebase configuration:', error.message);
  console.log('\n📝 Manual steps to get configuration:');
  console.log('1. Go to https://console.firebase.google.com/project/delawpr');
  console.log('2. Click the gear icon (Project Settings)');
  console.log('3. Scroll down to "Your apps" section');
  console.log('4. If no web app exists, click "Add app" → Web');
  console.log('5. Copy the configuration object');
  console.log('6. Update the values in .env.local');
}

console.log('\n🔗 Firebase Console: https://console.firebase.google.com/project/delawpr');
console.log('📄 After getting the config, update .env.local with the actual values');
