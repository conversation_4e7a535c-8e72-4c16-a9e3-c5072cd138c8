'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ProfileImageUpload } from '@/components/profile/profile-image-upload';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Save,
  Edit,
  Shield,
  Bell,
  CreditCard
} from 'lucide-react';

export function ClientProfile() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileImage, setProfileImage] = useState(user?.profileImage || '');
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    address: user?.address || '',
    city: user?.city || '',
    zipCode: user?.zipCode || ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleImageUpdate = (newImageUrl: string) => {
    setProfileImage(newImageUrl);
  };

  const handleSave = async () => {
    try {
      // Here you would implement the actual profile update logic
      console.log('Saving profile:', formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving profile:', error);
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white flex items-center">
          <User className="mr-3 h-8 w-8 text-yellow-500" />
          Mi Perfil
        </h1>
        <p className="mt-2 text-gray-400">
          Gestiona tu información personal y configuración de cuenta
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-white">Información Personal</CardTitle>
                  <CardDescription className="text-gray-400">
                    Actualiza tu información de contacto
                  </CardDescription>
                </div>
                <Button
                  onClick={() => isEditing ? handleSave() : setIsEditing(true)}
                  className="bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 hover:from-yellow-500 hover:via-orange-600 hover:to-yellow-500 text-black font-semibold"
                >
                  {isEditing ? (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Guardar
                    </>
                  ) : (
                    <>
                      <Edit className="mr-2 h-4 w-4" />
                      Editar
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-white">Nombre</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    disabled={!isEditing}
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 disabled:opacity-50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-white">Apellido</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    disabled={!isEditing}
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 disabled:opacity-50"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">Correo Electrónico</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 disabled:opacity-50"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="text-white">Teléfono</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 disabled:opacity-50"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address" className="text-white">Dirección</Label>
                <Input
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  disabled={!isEditing}
                  placeholder="Calle y número"
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 disabled:opacity-50"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city" className="text-white">Ciudad</Label>
                  <Input
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    disabled={!isEditing}
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 disabled:opacity-50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="zipCode" className="text-white">Código Postal</Label>
                  <Input
                    id="zipCode"
                    name="zipCode"
                    value={formData.zipCode}
                    onChange={handleChange}
                    disabled={!isEditing}
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 disabled:opacity-50"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Shield className="mr-2 h-5 w-5 text-yellow-500" />
                Seguridad
              </CardTitle>
              <CardDescription className="text-gray-400">
                Gestiona la seguridad de tu cuenta
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full bg-white/10 border border-white/20 text-white hover:bg-white/20">
                Cambiar Contraseña
              </Button>
              <Button className="w-full bg-white/10 border border-white/20 text-white hover:bg-white/20">
                Configurar Autenticación de Dos Factores
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Profile Image */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white text-center">Foto de Perfil</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ProfileImageUpload
                currentImageUrl={profileImage}
                onImageUpdate={handleImageUpdate}
                size="lg"
                className="w-full"
              />
            </CardContent>
          </Card>

          {/* Profile Summary */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-semibold text-white mb-1">
                {user?.firstName} {user?.lastName}
              </h3>
              <p className="text-gray-400 text-sm mb-4">Cliente</p>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-center space-x-2 text-gray-300">
                  <Mail className="h-4 w-4" />
                  <span>{user?.email}</span>
                </div>
                {user?.phone && (
                  <div className="flex items-center justify-center space-x-2 text-gray-300">
                    <Phone className="h-4 w-4" />
                    <span>{user.phone}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Acciones Rápidas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full bg-white/10 border border-white/20 text-white hover:bg-white/20 justify-start">
                <Bell className="mr-2 h-4 w-4" />
                Notificaciones
              </Button>
              <Button className="w-full bg-white/10 border border-white/20 text-white hover:bg-white/20 justify-start">
                <CreditCard className="mr-2 h-4 w-4" />
                Métodos de Pago
              </Button>
              <Button className="w-full bg-white/10 border border-white/20 text-white hover:bg-white/20 justify-start">
                <MapPin className="mr-2 h-4 w-4" />
                Preferencias de Ubicación
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
