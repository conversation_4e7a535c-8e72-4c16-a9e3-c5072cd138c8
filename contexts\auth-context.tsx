'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  User as FirebaseUser,
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged,
  updateProfile,
  UserCredential
} from 'firebase/auth';
import { doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { User } from '@/types';

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    password: string;
  }) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  firebaseUser: FirebaseUser | null;
};

// Create the context with a default value
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // Handle Firebase auth state changes
  useEffect(() => {
    if (!auth) {
      setIsLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // User is signed in
        setFirebaseUser(user);
        
        // Get additional user data from Firestore
        try {
          if (!db) {
            console.warn('Firestore is not initialized');
            setUser({
              id: user.uid,
              email: user.email || '',
              firstName: user.displayName?.split(' ')[0] || 'Usuario',
              lastName: user.displayName?.split(' ')[1] || '',
              phone: '',
              role: 'client',
              profileImage: '',
              isActive: true,
              emailVerified: false,
              preferences: {
                language: 'es',
                notifications: {
                  email: true,
                  sms: true,
                  push: true
                }
              },
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            });
            setIsLoading(false);
            return;
          }

          const userDoc = await getDoc(doc(db, 'users', user.uid));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            setUser({
              id: user.uid,
              email: user.email || '',
              firstName: userData.firstName || 'Usuario',
              lastName: userData.lastName || '',
              phone: userData.phone || '',
              role: userData.role || 'client',
              profileImage: userData.profileImage || '',
              isActive: userData.isActive ?? true,
              emailVerified: userData.emailVerified ?? false,
              preferences: userData.preferences || {
                language: 'es',
                notifications: {
                  email: true,
                  sms: true,
                  push: true
                }
              },
              createdAt: userData.createdAt,
              updatedAt: userData.updatedAt
            });
          } else {
            // If no user doc exists, create one with default values
            const newUser = {
              id: user.uid,
              email: user.email || '',
              firstName: user.displayName?.split(' ')[0] || 'Usuario',
              lastName: user.displayName?.split(' ')[1] || '',
              phone: '',
              role: 'client' as const,
              profileImage: '',
              isActive: true,
              emailVerified: false,
              preferences: {
                language: 'es' as const,
                notifications: {
                  email: true,
                  sms: true,
                  push: true
                }
              },
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            };
            await setDoc(doc(db, 'users', user.uid), newUser);
            setUser({
              ...newUser,
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            });
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          setUser({
            id: user.uid,
            email: user.email || '',
            firstName: user.displayName?.split(' ')[0] || 'Usuario',
            lastName: user.displayName?.split(' ')[1] || '',
            phone: '',
            role: 'client',
            profileImage: '',
            isActive: true,
            emailVerified: false,
            preferences: {
              language: 'es',
              notifications: {
                email: true,
                sms: true,
                push: true
              }
            },
            createdAt: new Date() as any,
            updatedAt: new Date() as any
          });
        }
      } else {
        // User is signed out
        setUser(null);
        setFirebaseUser(null);
      }
      setIsLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      if (!auth) {
        return { success: false, error: 'Firebase authentication is not initialized' };
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const idToken = await userCredential.user.getIdToken();
      
      // Set auth token in cookie with proper attributes
      if (typeof window !== 'undefined') {
        const expires = new Date();
        expires.setDate(expires.getDate() + 7); // 7 days
        document.cookie = `auth-token=${idToken}; path=/; expires=${expires.toUTCString()}; SameSite=Lax; Secure`;
      }
      
      // Force a page reload to ensure all auth state is properly updated
      if (typeof window !== 'undefined') {
        window.location.href = '/dashboard';
      }
      
      return { success: true };
    } catch (error: any) {
      console.error('Login error:', error);
      return { 
        success: false, 
        error: error.code === 'auth/wrong-password' || error.code === 'auth/user-not-found'
          ? 'Correo o contraseña incorrectos.'
          : 'Error al iniciar sesión. Intenta de nuevo más tarde.'
      };
    }
  };

  const register = async (userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    password: string;
  }) => {
    try {
      const { email, password, firstName, lastName, phone } = userData;

      if (!auth || !db) {
        console.error('Firebase services not initialized:', { auth: !!auth, db: !!db });
        return { success: false, error: 'Firebase services are not initialized. Please check your configuration.' };
      }

      console.log('Attempting to create user with Firebase Auth...');
      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Create user in Firestore
      await setDoc(doc(db, 'users', user.uid), {
        id: user.uid,
        email,
        firstName,
        lastName,
        phone,
        role: 'client',
        profileImage: '',
        isActive: true,
        emailVerified: false,
        preferences: {
          language: 'es',
          notifications: {
            email: true,
            sms: true,
            push: true
          }
        },
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      // Set auth token in cookie with proper attributes
      const idToken = await userCredential.user.getIdToken();
      if (typeof window !== 'undefined') {
        const expires = new Date();
        expires.setDate(expires.getDate() + 7); // 7 days
        document.cookie = `auth-token=${idToken}; path=/; expires=${expires.toUTCString()}; SameSite=Lax; Secure`;
      }
      
      // Update local state
      setUser({
        id: user.uid,
        email: email,
        firstName,
        lastName,
        phone: phone,
        role: 'client',
        profileImage: '',
        isActive: true,
        emailVerified: false,
        preferences: {
          language: 'es',
          notifications: {
            email: true,
            sms: true,
            push: true
          }
        },
        createdAt: new Date() as any, // Will be replaced by server timestamp
        updatedAt: new Date() as any
      });
      
      // Force a page reload to ensure all auth state is properly updated
      if (typeof window !== 'undefined') {
        window.location.href = '/dashboard';
      }
      
      return { success: true };
    } catch (error: any) {
      console.error('Registration error:', error);

      let errorMessage = 'Error al crear la cuenta. Intenta de nuevo más tarde.';

      if (error.code) {
        switch (error.code) {
          case 'auth/email-already-in-use':
            errorMessage = 'Este correo ya está registrado.';
            break;
          case 'auth/configuration-not-found':
            errorMessage = 'Firebase Authentication no está configurado correctamente. Por favor contacta al administrador.';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Correo electrónico inválido.';
            break;
          case 'auth/weak-password':
            errorMessage = 'La contraseña es muy débil.';
            break;
          default:
            errorMessage = `Error: ${error.message}`;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  };

  const logout = async () => {
    try {
      if (!auth) {
        console.warn('Firebase auth is not initialized');
        return;
      }

      await firebaseSignOut(auth);
      // Clear auth token cookie
      if (typeof window !== 'undefined') {
        document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT;';
        // Force a full page reload to clear all state
        window.location.href = '/login';
      }
      setUser(null);
      setFirebaseUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Still try to redirect even if there was an error
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
  };

  const value = {
    user,
    isLoading,
    login,
    register,
    logout,
    firebaseUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
