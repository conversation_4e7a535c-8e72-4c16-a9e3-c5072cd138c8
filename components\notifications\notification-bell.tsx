'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  X, 
  <PERSON>, 
  CheckCheck, 
  Trash2,
  Settings,
  Calendar,
  MessageSquare,
  DollarSign,
  Star,
  User,
  AlertTriangle,
  Info,
  Shield
} from 'lucide-react';
import { useNotifications } from '@/contexts/notification-context';
import { Notification, NotificationType } from '@/types/notifications';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'appointment_request':
    case 'appointment_confirmed':
    case 'appointment_cancelled':
    case 'appointment_reminder':
    case 'appointment_completed':
    case 'appointment_rescheduled':
      return Calendar;
    
    case 'new_message':
    case 'message_reply':
      return MessageSquare;
    
    case 'payment_received':
    case 'payment_pending':
    case 'payment_failed':
    case 'invoice_generated':
      return DollarSign;
    
    case 'new_review':
    case 'review_response':
      return Star;
    
    case 'profile_incomplete':
    case 'profile_verified':
    case 'profile_updated':
      return User;
    
    case 'account_security':
      return Shield;
    
    case 'system_maintenance':
    case 'system_update':
      return AlertTriangle;
    
    default:
      return Info;
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'urgent':
      return 'from-red-400 to-red-600';
    case 'high':
      return 'from-orange-400 to-orange-600';
    case 'medium':
      return 'from-yellow-400 to-amber-500';
    case 'low':
      return 'from-blue-400 to-blue-600';
    default:
      return 'from-gray-400 to-gray-600';
  }
};

export function NotificationBell() {
  const { 
    notifications, 
    unreadCount, 
    isLoading, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification 
  } = useNotifications();
  
  const [isOpen, setIsOpen] = useState(false);

  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.read) {
      await markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
    
    setIsOpen(false);
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  const handleDeleteNotification = async (e: React.MouseEvent, notificationId: string) => {
    e.stopPropagation();
    await deleteNotification(notificationId);
  };

  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: es 
      });
    } catch {
      return 'Hace un momento';
    }
  };

  return (
    <div className="relative">
      {/* Notification Bell Button */}
      <Button 
        onClick={() => setIsOpen(!isOpen)}
        className="bg-black/40 backdrop-blur-xl border border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10 hover:border-yellow-500/50 rounded-xl px-4 py-2 transition-all duration-300 relative"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center">
            <span className="text-xs font-bold text-white">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          </div>
        )}
      </Button>

      {/* Notification Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Notification Panel */}
          <Card className="absolute right-0 top-full mt-2 w-96 max-h-96 bg-gradient-to-br from-black/90 via-black/80 to-black/90 backdrop-blur-2xl border border-yellow-500/20 rounded-2xl shadow-2xl z-50 overflow-hidden">
            <CardHeader className="p-4 border-b border-white/10">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Notificaciones</CardTitle>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <Button
                      onClick={handleMarkAllAsRead}
                      size="sm"
                      className="bg-yellow-500/20 border border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/30 hover:border-yellow-500/50 rounded-xl px-3 py-1 text-xs"
                    >
                      <CheckCheck className="h-3 w-3 mr-1" />
                      Marcar todas
                    </Button>
                  )}
                  <Button
                    onClick={() => setIsOpen(false)}
                    size="sm"
                    className="bg-white/10 border border-white/20 text-white hover:bg-white/20 hover:border-white/30 rounded-xl px-2 py-1"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="p-0 max-h-80 overflow-y-auto">
              {isLoading ? (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto"></div>
                  <p className="text-gray-400 mt-2">Cargando notificaciones...</p>
                </div>
              ) : notifications.length > 0 ? (
                <div className="space-y-1">
                  {notifications.slice(0, 10).map((notification) => {
                    const Icon = getNotificationIcon(notification.type);
                    const priorityColor = getPriorityColor(notification.priority);
                    
                    return (
                      <div
                        key={notification.id}
                        onClick={() => handleNotificationClick(notification)}
                        className={`group p-4 hover:bg-white/5 cursor-pointer transition-all duration-200 border-l-4 ${
                          notification.read 
                            ? 'border-transparent bg-black/20' 
                            : `border-yellow-500 bg-gradient-to-r ${priorityColor} bg-opacity-10`
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-10 h-10 rounded-xl flex items-center justify-center bg-gradient-to-br ${priorityColor} bg-opacity-20`}>
                            <Icon className={`h-5 w-5 ${notification.read ? 'text-gray-400' : 'text-white'}`} />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <h4 className={`text-sm font-semibold ${notification.read ? 'text-gray-300' : 'text-white'}`}>
                                {notification.title}
                              </h4>
                              <div className="flex items-center space-x-1 ml-2">
                                {!notification.read && (
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                )}
                                <Button
                                  onClick={(e) => handleDeleteNotification(e, notification.id)}
                                  size="sm"
                                  className="opacity-0 group-hover:opacity-100 bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30 hover:border-red-500/50 rounded-lg px-1 py-1 transition-all duration-200"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            
                            <p className={`text-xs mt-1 ${notification.read ? 'text-gray-500' : 'text-gray-300'}`}>
                              {notification.message}
                            </p>
                            
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-gray-500">
                                {formatTimeAgo(notification.createdAt)}
                              </span>
                              
                              {notification.priority === 'urgent' && (
                                <Badge className="bg-red-500/20 border border-red-500/30 text-red-400 text-xs px-2 py-0">
                                  Urgente
                                </Badge>
                              )}
                              
                              {notification.actionText && (
                                <span className="text-xs text-yellow-400 font-medium">
                                  {notification.actionText} →
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  
                  {notifications.length > 10 && (
                    <div className="p-4 text-center border-t border-white/10">
                      <Button
                        onClick={() => {
                          window.location.href = '/es/dashboard/notifications';
                          setIsOpen(false);
                        }}
                        className="bg-yellow-500/20 border border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/30 hover:border-yellow-500/50 rounded-xl px-4 py-2 text-sm"
                      >
                        Ver todas las notificaciones
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-6 text-center">
                  <Bell className="h-12 w-12 text-gray-500 mx-auto mb-3" />
                  <h3 className="text-white font-semibold mb-1">No hay notificaciones</h3>
                  <p className="text-gray-400 text-sm">
                    Te notificaremos cuando tengas nuevas actualizaciones
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
