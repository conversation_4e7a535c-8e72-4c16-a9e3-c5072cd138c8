const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: "delawpr"
  });
}

const db = admin.firestore();
const auth = admin.auth();

async function createDummyLawyer() {
  try {
    console.log('Creating dummy lawyer account...');
    
    // Lawyer account details
    const lawyerData = {
      email: '<EMAIL>',
      password: 'LawyerTest123!',
      firstName: 'María',
      lastName: 'González',
      phone: '(*************',
      role: 'lawyer'
    };

    // Create Firebase Auth user
    console.log('Creating Firebase Auth user...');
    const user = await auth.createUser({
      email: lawyerData.email,
      password: lawyerData.password,
      displayName: `${lawyerData.firstName} ${lawyerData.lastName}`,
      emailVerified: true
    });

    console.log('Firebase Auth user created:', user.uid);

    // Create user document
    console.log('Creating user document...');
    await db.collection('users').doc(user.uid).set({
      id: user.uid,
      email: lawyerData.email,
      firstName: lawyerData.firstName,
      lastName: lawyerData.lastName,
      phone: lawyerData.phone,
      role: lawyerData.role,
      profileImage: '',
      isActive: true,
      emailVerified: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      preferences: {
        language: 'es',
        notifications: {
          email: true,
          sms: true,
          push: true
        }
      }
    });

    // Create lawyer profile document
    console.log('Creating lawyer profile...');
    await db.collection('lawyers').doc(user.uid).set({
      id: user.uid,
      firstName: lawyerData.firstName,
      lastName: lawyerData.lastName,
      email: lawyerData.email,
      phone: lawyerData.phone,
      specialty: 'family',
      specialtyName: 'Derecho de Familia',
      location: 'San Juan, PR',
      description: 'Especialista en derecho de familia con más de 15 años de experiencia. Casos exitosos en divorcios, custodia y adopción.',
      price: '150',
      priceType: 'por hora',
      languages: ['Español', 'Inglés'],
      education: [
        'Universidad de Puerto Rico - Juris Doctor',
        'Universidad Interamericana - Bachillerato en Ciencias Políticas'
      ],
      experience: '15 años de experiencia en derecho de familia',
      certifications: [
        'Colegio de Abogados de Puerto Rico',
        'Certificación en Mediación Familiar'
      ],
      services: [
        'Divorcios',
        'Custodia de menores',
        'Adopción',
        'Pensión alimentaria',
        'Violencia doméstica'
      ],
      profileImage: '',
      rating: 4.9,
      reviewCount: 127,
      isVerified: true,
      profileVisible: true,
      subscriptionStatus: 'active',
      availability: {
        monday: '9:00 AM - 5:00 PM',
        tuesday: '9:00 AM - 5:00 PM',
        wednesday: '9:00 AM - 5:00 PM',
        thursday: '9:00 AM - 5:00 PM',
        friday: '9:00 AM - 3:00 PM',
        saturday: 'Cerrado',
        sunday: 'Cerrado'
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Create lawyer subscription
    console.log('Creating lawyer subscription...');
    await db.collection('lawyerSubscriptions').doc(user.uid).set({
      id: user.uid,
      lawyerId: user.uid,
      plan: 'monthly',
      status: 'active',
      stripeCustomerId: 'dummy_customer_id',
      stripeSubscriptionId: 'dummy_subscription_id',
      currentPeriodStart: new Date().toISOString(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log('✅ Dummy lawyer account created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: LawyerTest123!');
    console.log('👤 Name: María González');
    console.log('🏢 Specialty: Derecho de Familia');
    console.log('📍 Location: San Juan, PR');
    console.log('💳 Subscription: Active (Monthly)');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating dummy lawyer:', error);
    process.exit(1);
  }
}

createDummyLawyer();
