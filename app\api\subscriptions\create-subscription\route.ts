import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { SubscriptionService } from '@/services/subscription-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      planId, 
      lawyerId, 
      lawyerEmail, 
      lawyerName 
    } = body;

    // Validate required fields
    if (!planId || !lawyerId || !lawyerEmail || !lawyerName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get plan details
    const plan = SubscriptionService.PLANS.find(p => p.id === planId);
    if (!plan) {
      return NextResponse.json(
        { error: 'Invalid plan ID' },
        { status: 400 }
      );
    }

    // Create or get Stripe customer
    let customer;
    try {
      // Try to find existing customer
      const existingCustomers = await stripe.customers.list({
        email: lawyerEmail,
        limit: 1
      });

      if (existingCustomers.data.length > 0) {
        customer = existingCustomers.data[0];
      } else {
        // Create new customer
        customer = await stripe.customers.create({
          email: lawyer<PERSON>mail,
          name: lawyer<PERSON><PERSON>,
          metadata: {
            lawyerId,
            userType: 'lawyer'
          }
        });
      }
    } catch (error) {
      console.error('Error creating/finding customer:', error);
      return NextResponse.json(
        { error: 'Error setting up customer account' },
        { status: 500 }
      );
    }

    // Create subscription with Stripe
    try {
      // First create a product
      const product = await stripe.products.create({
        name: plan.name,
        description: `Suscripción ${plan.interval === 'month' ? 'mensual' : 'anual'} para abogados`,
      });

      // Then create a price for the product
      const price = await stripe.prices.create({
        currency: plan.currency,
        unit_amount: Math.round(plan.price * 100), // Convert to cents
        recurring: {
          interval: plan.interval,
        },
        product: product.id,
      });

      // Create subscription with the price
      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [
          {
            price: price.id,
          },
        ],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          lawyerId,
          planId,
          lawyerName
        }
      });

      // Calculate subscription period
      const now = new Date();
      const periodEnd = new Date(now);
      if (plan.interval === 'month') {
        periodEnd.setMonth(periodEnd.getMonth() + 1);
      } else {
        periodEnd.setFullYear(periodEnd.getFullYear() + 1);
      }

      // Create subscription record in Firestore
      const subscriptionId = await SubscriptionService.createSubscription({
        lawyerId,
        lawyerEmail,
        lawyerName,
        status: 'inactive', // Will be updated to active when payment succeeds
        plan: plan.interval,
        amount: plan.price,
        currency: plan.currency,
        stripeCustomerId: customer.id,
        stripeSubscriptionId: subscription.id,
        currentPeriodStart: now.toISOString(),
        currentPeriodEnd: periodEnd.toISOString(),
        nextBillingDate: periodEnd.toISOString(),
        cancelAtPeriodEnd: false,
        termsAcceptedAt: new Date().toISOString(),
        termsVersion: '1.0'
      });

      // Get client secret from payment intent
      const invoice = subscription.latest_invoice as any;
      const paymentIntent = invoice.payment_intent;

      return NextResponse.json({
        subscriptionId,
        clientSecret: paymentIntent.client_secret,
        customerId: customer.id,
        stripeSubscriptionId: subscription.id
      });

    } catch (error: any) {
      console.error('Error creating subscription:', error);
      return NextResponse.json(
        { error: error.message || 'Error creating subscription' },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Error in subscription creation:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
